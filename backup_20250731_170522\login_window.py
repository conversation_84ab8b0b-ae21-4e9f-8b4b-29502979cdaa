#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول مع دعم خط Cairo والاتجاه العربي
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from cairo_fonts import font_manager, setup_widget_rtl
from text_clarity_fix import TextClarityFixer
from styles import COLORS, BUTTON_STYLES, INPUT_STYLES, ARABIC_FONT, FONTS, LOGIN_STYLE

class LoginWindow(QDialog):
    def __init__(self):
        super().__init__()

        # إعداد محسن الوضوح
        self.clarity_fixer = TextClarityFixer()
        app = QApplication.instance()
        if app:
            self.clarity_fixer.setup_high_quality_rendering(app)

        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("تسجيل الدخول - عيادة الدكتورة ضياء أبو جلبان")
        self.setFixedSize(500, 700)
        self.setWindowFlags(Qt.FramelessWindowHint)

        # ضبط الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق النمط العام
        self.setStyleSheet(f"""
            QDialog {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                           stop:0 {COLORS['primary']}, 
                                           stop:1 {COLORS['secondary']});
            }}
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setAlignment(Qt.AlignCenter)
        
        # بطاقة تسجيل الدخول
        login_card = QFrame()
        login_card.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border-radius: 20px;
                padding: 40px;
            }}
        """)
        login_card.setFixedSize(420, 600)
        
        card_layout = QVBoxLayout(login_card)
        card_layout.setSpacing(20)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # الشعار والعنوان
        self.create_logo_section(card_layout)
        
        # حقول تسجيل الدخول
        self.create_login_form(card_layout)
        
        # أزرار التحكم
        self.create_buttons(card_layout)
        
        main_layout.addWidget(login_card)
        self.setLayout(main_layout)
        
        # تطبيق الخط العربي
        font = QFont("Arial", 12)
        font.setFamily("Segoe UI")
        self.setFont(font)
        
    def create_logo_section(self, layout):
        """إنشاء قسم الشعار والعنوان"""
        
        # أيقونة الأسنان (شعار مميز)
        logo_label = QLabel("🦷")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet(f"""
            QLabel {{
                font-size: 80px;
                color: {COLORS['primary']};
                margin-bottom: 10px;
                background-color: {COLORS['background']};
                border-radius: 50px;
                padding: 20px;
                min-width: 100px;
                min-height: 100px;
            }}
        """)
        layout.addWidget(logo_label)
        
        # اسم العيادة مع خط فائق الوضوح
        clinic_name = QLabel("عيادة الدكتورة ضياء أبو جلبان")
        clinic_name.setAlignment(Qt.AlignCenter)

        # تطبيق خط فائق الوضوح
        clear_font = self.clarity_fixer.create_crystal_clear_font(ARABIC_FONT, FONTS['size_header'])
        clinic_name.setFont(clear_font)

        clinic_name.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-weight: 600;
                margin-bottom: 10px;
                padding: 8px;
                letter-spacing: 0.5px;
                text-align: center;
            }}
        """)
        layout.addWidget(clinic_name)

        # التخصص مع خط واضح
        specialty = QLabel("لطب وجراحة الفم والأسنان")
        specialty.setAlignment(Qt.AlignCenter)

        # تطبيق خط واضح للتخصص
        specialty_font = self.clarity_fixer.create_crystal_clear_font(ARABIC_FONT, FONTS['size_large'])
        specialty.setFont(specialty_font)
        specialty.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_secondary']};
                font-family: '{ARABIC_FONT}';
                font-size: {FONTS['size_large']}px;
                margin-bottom: 30px;
            }}
        """)
        layout.addWidget(specialty)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet(f"""
            QFrame {{
                color: {COLORS['border']};
                background-color: {COLORS['border']};
                height: 2px;
                margin: 10px 0px;
            }}
        """)
        layout.addWidget(line)
        
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        
        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-family: '{ARABIC_FONT}';
                font-size: {FONTS['size_title']}px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(login_title)

        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-family: '{ARABIC_FONT}';
                font-size: {FONTS['size_medium']}px;
                font-weight: 600;
                margin-bottom: 5px;
            }}
        """)
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet(INPUT_STYLES)
        self.username_input.setFixedHeight(50)
        layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 5px;
                margin-top: 15px;
            }}
        """)
        layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(INPUT_STYLES)
        self.password_input.setFixedHeight(50)
        layout.addWidget(self.password_input)
        
        # خانة تذكرني
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {COLORS['text_secondary']};
                font-size: 14px;
                spacing: 8px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid {COLORS['border']};
                background-color: {COLORS['surface']};
            }}
            QCheckBox::indicator:checked {{
                background-color: {COLORS['primary']};
                border-color: {COLORS['primary']};
            }}
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()
        layout.addLayout(remember_layout)
        
    def create_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setStyleSheet(BUTTON_STYLES['primary'])
        self.login_button.setFixedHeight(50)
        self.login_button.clicked.connect(self.handle_login)
        layout.addWidget(self.login_button)
        
        # زر الإغلاق
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet(BUTTON_STYLES['outline'])
        self.close_button.setFixedHeight(45)
        self.close_button.clicked.connect(self.close)
        layout.addWidget(self.close_button)
        
        # معلومات إضافية
        info_label = QLabel("للدعم الفني: تواصل مع المطور")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_secondary']};
                font-size: 12px;
                margin-top: 20px;
            }}
        """)
        layout.addWidget(info_label)
        
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        # التحقق من البيانات (يمكن تطويرها لاحقاً)
        if not username or not password:
            self.show_error("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
            
        # تسجيل دخول مؤقت (يمكن تطويره لاحقاً)
        if username == "admin" and password == "123456":
            self.accept()  # إغلاق نافذة تسجيل الدخول بنجاح
        else:
            self.show_error("اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("خطأ في تسجيل الدخول")
        msg.setText(message)
        msg.setStyleSheet(f"""
            QMessageBox {{
                background-color: {COLORS['surface']};
                color: {COLORS['text_primary']};
            }}
            QMessageBox QPushButton {{
                {BUTTON_STYLES['primary']}
                min-width: 80px;
            }}
        """)
        msg.exec_()
        
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        super().keyPressEvent(event)
