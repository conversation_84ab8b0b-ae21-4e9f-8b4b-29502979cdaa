# دليل حل مشاكل اللغة العربية

## 🔧 حل مشاكل عرض النص العربي

### المشاكل الشائعة والحلول

#### 1. النص العربي يظهر كرموز غريبة أو مربعات
**الأعراض:**
- ظهور □□□ بدلاً من النص العربي
- رموز غريبة مثل ??? أو ####

**الحلول:**
```bash
# تشغيل أداة إصلاح الترميز
python arabic_fix.py

# إعادة ضبط الإعدادات
python arabic_settings.py

# إعادة تشغيل التطبيق
python main.py
```

#### 2. النص العربي يظهر من اليسار لليمين
**الأعراض:**
- النص العربي يبدأ من الجهة الخاطئة
- الكلمات مقلوبة أو مبعثرة

**الحلول:**
- التطبيق يضبط الاتجاه تلقائياً
- تأكد من إعدادات اللغة في Windows
- أعد تشغيل التطبيق

#### 3. الخط غير واضح أو مشوه
**الأعراض:**
- النص العربي غير مقروء
- الحروف متداخلة أو مقطوعة

**الحلول:**
```bash
# اختبار الخطوط المتاحة
python setup_cairo.py

# تشغيل نافذة اختبار الخطوط
python font_test.py
```

---

## 🛠️ أدوات التشخيص والإصلاح

### 1. أداة التشخيص الشامل
```bash
python arabic_fix.py
```
**ما تفعله:**
- تشخيص مشاكل الترميز
- اختبار الخطوط المتاحة
- عرض نافذة اختبار النص العربي
- إصلاح إعدادات locale

### 2. أداة إعداد الخطوط
```bash
python arabic_settings.py
```
**ما تفعله:**
- اختيار أفضل خط عربي متاح
- ضبط إعدادات الترميز
- اختبار جودة عرض النص

### 3. أداة اختبار الخطوط
```bash
python font_test.py
```
**ما تفعله:**
- عرض نافذة اختبار تفاعلية
- مقارنة خطوط مختلفة
- اختبار النص العربي بأحجام مختلفة

---

## 📋 قائمة فحص سريعة

### تحقق من هذه النقاط إذا كان النص العربي لا يعمل:

- [ ] **إعدادات Windows**
  - [ ] تثبيت حزمة اللغة العربية
  - [ ] ضبط المنطقة إلى السعودية أو دولة عربية
  - [ ] تفعيل دعم اللغات من اليمين لليسار

- [ ] **إعدادات التطبيق**
  - [ ] تشغيل `python arabic_settings.py` بنجاح
  - [ ] ظهور رسالة "✅ جاهز" في التقرير
  - [ ] عدم وجود رسائل خطأ عند التشغيل

- [ ] **اختبار النص**
  - [ ] تشغيل `python arabic_fix.py`
  - [ ] النص يظهر بوضوح في نافذة الاختبار
  - [ ] الاتجاه من اليمين لليسار صحيح

---

## 🔍 رسائل الخطأ الشائعة

### "UnicodeDecodeError" أو "UnicodeEncodeError"
**السبب:** مشكلة في ترميز النص
**الحل:**
```bash
python arabic_settings.py
```

### "Font family not found"
**السبب:** الخط المطلوب غير متاح
**الحل:** التطبيق سيختار خط بديل تلقائياً

### النص يظهر كـ "????"
**السبب:** مشكلة في دعم Unicode
**الحل:**
1. تأكد من تثبيت حزمة اللغة العربية في Windows
2. تشغيل `python arabic_fix.py`
3. إعادة تشغيل الكمبيوتر إذا لزم الأمر

---

## 🎯 نصائح لتحسين جودة النص

### 1. إعدادات Windows
- **الانتقال إلى:** Settings > Time & Language > Language
- **إضافة:** Arabic (Saudi Arabia) كلغة مفضلة
- **تثبيت:** Language pack و Handwriting features

### 2. إعدادات العرض
- **دقة الشاشة:** 100% أو أعلى (تجنب 75%)
- **ClearType:** تفعيل ClearType في Windows
- **High DPI:** التطبيق يدعم High DPI تلقائياً

### 3. إعدادات التطبيق
- **الخط المفضل:** Segoe UI (الافتراضي)
- **حجم الخط:** 12-14 للنص العادي
- **الاتجاه:** RTL (من اليمين لليسار)

---

## 🚨 حلول الطوارئ

### إذا لم يعمل أي شيء:

#### الحل الأول: إعادة تعيين كاملة
```bash
# حذف ملفات التكوين المؤقتة
del font_config.py
del __pycache__

# إعادة إعداد كاملة
python arabic_settings.py
python main.py
```

#### الحل الثاني: استخدام خط النظام
```bash
# تحرير ملف styles.py
# تغيير ARABIC_FONT إلى "Arial" أو "Times New Roman"
```

#### الحل الثالث: تشغيل بدون RTL
```bash
# تعديل مؤقت في main.py
# تعليق السطر: app.setLayoutDirection(Qt.RightToLeft)
```

---

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم الفني:

#### تشغيل تقرير التشخيص:
```bash
python arabic_fix.py > arabic_report.txt
```

#### معلومات النظام:
- نظام التشغيل: Windows
- إصدار Python: 3.x
- إصدار PyQt5: 5.x
- Locale المضبوط: ar_SA.UTF-8
- الخط المستخدم: Segoe UI

#### ملفات السجل:
- رسائل التطبيق تظهر في Terminal
- ملف التكوين: `font_config.py`
- تقرير التشخيص: `arabic_report.txt`

---

## ✅ علامات النجاح

### يجب أن ترى هذه الرسائل عند التشغيل الناجح:

```
✅ تم إصلاح ترميز UTF-8
✅ تم ضبط locale: ar_SA.UTF-8
✅ تم اختيار الخط: Segoe UI
🧪 اختبار عرض النص العربي:
   ✅ عيادة الدكتورة ضياء أبو جلبان
   ✅ إدارة المرضى والمواعيد
   ✅ الأرقام: ١٢٣٤٥٦٧٨٩٠
📋 تقرير الحالة:
   overall: ✅ جاهز
```

### في واجهة التطبيق:
- النص العربي واضح ومقروء
- الاتجاه من اليمين لليسار
- الأزرار والقوائم في المكان الصحيح
- لا توجد رموز غريبة أو مربعات فارغة

---

## 🔄 التحديثات المستقبلية

### تحسينات مخططة:
- [ ] دعم خطوط عربية إضافية (Cairo, Amiri)
- [ ] إعدادات خطوط قابلة للتخصيص
- [ ] تحسين أداء عرض النص
- [ ] دعم أفضل للأرقام العربية الهندية

### كيفية التحديث:
1. تحميل الإصدار الجديد
2. تشغيل `python arabic_settings.py`
3. إعادة تشغيل التطبيق

---

**💡 تذكر:** معظم مشاكل النص العربي تُحل بتشغيل `python arabic_fix.py` وإعادة تشغيل التطبيق!
