@echo off
chcp 65001 > nul
title نظام إدارة عيادة الأسنان - عيادة الدكتورة ضياء أبو جلبان

echo.
echo ========================================
echo    نظام إدارة عيادة الأسنان
echo    عيادة الدكتورة ضياء أبو جلبان
echo ========================================
echo.

echo جاري التحقق من متطلبات التشغيل...
echo.

REM التحقق من وجود Python
REM تحديد مسار بايثون من البيئة الافتراضية
set PYTHON_VENV_PATH=%~dp0.venv\Scripts\python.exe

REM التحقق من وجود Python
"%PYTHON_VENV_PATH%" --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: البيئة الافتراضية غير موجودة أو بايثون غير مثبت
    echo يرجى التأكد من وجود المجلد .venv
    pause
    exit /b 1
)

echo ✅ Python مثبت بنجاح
echo.

REM التحقق من وجود PyQt5
REM التحقق من وجود PyQt5
"%PYTHON_VENV_PATH%" -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: PyQt5 غير مثبت في البيئة الافتراضية
    echo جاري تثبيت المتطلبات...
    "%PYTHON_VENV_PATH%" -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo جاري تشغيل النظام...
echo.

REM تشغيل التطبيق
REM تشغيل التطبيق من البيئة الافتراضية
"%PYTHON_VENV_PATH%" main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo يرجى التحقق من ملفات النظام
    pause
    exit /b 1
)

echo.
echo تم إغلاق النظام بنجاح
pause
