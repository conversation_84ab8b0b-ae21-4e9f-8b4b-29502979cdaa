#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحميل خط Cairo العربي من Google Fonts
"""

import os
import urllib.request
import zipfile
import tempfile

def download_cairo_font():
    """تحميل خط Cairo من Google Fonts"""
    
    # إنشاء مجلد الخطوط إذا لم يكن موجوداً
    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
    if not os.path.exists(fonts_dir):
        os.makedirs(fonts_dir)
    
    # التحقق من وجود الخطوط مسبقاً
    cairo_files = [
        "Cairo-Regular.ttf",
        "Cairo-Bold.ttf",
        "Cairo-SemiBold.ttf",
        "Cairo-Light.ttf"
    ]
    
    existing_fonts = 0
    for font_file in cairo_files:
        if os.path.exists(os.path.join(fonts_dir, font_file)):
            existing_fonts += 1
    
    if existing_fonts >= 2:
        print(f"✅ تم العثور على {existing_fonts} ملفات خط Cairo موجودة")
        return True
    
    print("🔄 بدء تحميل خط Cairo...")
    
    try:
        # رابط تحميل خط Cairo من Google Fonts
        url = "https://fonts.google.com/download?family=Cairo"
        
        # تحميل الملف المضغوط
        with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
            print("📥 تحميل الملف المضغوط...")
            urllib.request.urlretrieve(url, temp_file.name)
            
            # استخراج الملفات
            print("📂 استخراج ملفات الخط...")
            with zipfile.ZipFile(temp_file.name, 'r') as zip_ref:
                for file_info in zip_ref.infolist():
                    if file_info.filename.endswith('.ttf'):
                        # استخراج ملف الخط
                        zip_ref.extract(file_info, fonts_dir)
                        print(f"✅ تم استخراج: {file_info.filename}")
            
            # حذف الملف المؤقت
            os.unlink(temp_file.name)
        
        print("🎉 تم تحميل خط Cairo بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ فشل في تحميل خط Cairo: {e}")
        
        # محاولة تحميل بديل من مصدر آخر
        try:
            print("🔄 محاولة تحميل من مصدر بديل...")
            return download_cairo_alternative()
        except:
            print("❌ فشل في تحميل الخط من جميع المصادر")
            return False

def download_cairo_alternative():
    """تحميل خط Cairo من مصدر بديل"""
    
    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
    
    # روابط بديلة لملفات خط Cairo
    alternative_urls = {
        "Cairo-Regular.ttf": "https://github.com/google/fonts/raw/main/ofl/cairo/Cairo-Regular.ttf",
        "Cairo-Bold.ttf": "https://github.com/google/fonts/raw/main/ofl/cairo/Cairo-Bold.ttf",
        "Cairo-SemiBold.ttf": "https://github.com/google/fonts/raw/main/ofl/cairo/Cairo-SemiBold.ttf",
        "Cairo-Light.ttf": "https://github.com/google/fonts/raw/main/ofl/cairo/Cairo-Light.ttf"
    }
    
    downloaded = 0
    for filename, url in alternative_urls.items():
        try:
            file_path = os.path.join(fonts_dir, filename)
            if not os.path.exists(file_path):
                print(f"📥 تحميل {filename}...")
                urllib.request.urlretrieve(url, file_path)
                print(f"✅ تم تحميل {filename}")
                downloaded += 1
            else:
                print(f"✅ {filename} موجود مسبقاً")
                downloaded += 1
        except Exception as e:
            print(f"❌ فشل في تحميل {filename}: {e}")
    
    return downloaded >= 2

def create_sample_fonts():
    """إنشاء ملفات خطوط تجريبية إذا فشل التحميل"""
    
    fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
    
    # إنشاء ملف README في مجلد الخطوط
    readme_path = os.path.join(fonts_dir, 'README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write("""
مجلد خطوط Cairo العربية

لتحسين جودة النص العربي، يمكنك:

1. تحميل خط Cairo من Google Fonts:
   https://fonts.google.com/specimen/Cairo

2. نسخ ملفات .ttf إلى هذا المجلد:
   - Cairo-Regular.ttf
   - Cairo-Bold.ttf
   - Cairo-SemiBold.ttf
   - Cairo-Light.ttf

3. إعادة تشغيل التطبيق

ملاحظة: التطبيق سيعمل بدون هذه الخطوط باستخدام خطوط النظام الافتراضية.
        """)
    
    print(f"📝 تم إنشاء ملف README في: {readme_path}")

if __name__ == "__main__":
    print("🔤 أداة تحميل خط Cairo العربي")
    print("=" * 40)
    
    success = download_cairo_font()
    
    if not success:
        print("\n⚠️ لم يتم تحميل الخطوط، سيتم إنشاء ملف إرشادات...")
        create_sample_fonts()
    
    print("\n✅ انتهت عملية إعداد الخطوط")
    print("🔄 يمكنك الآن تشغيل التطبيق: python main.py")
