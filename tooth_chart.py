"""
خريطة الأسنان التفاعلية
FDI Tooth Numbering System
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from styles import COLORS, get_tooth_color

class ToothButton(QPushButton):
    """زر السن التفاعلي"""
    tooth_clicked = pyqtSignal(str, str)  # رقم السن، نوع السن
    
    def __init__(self, tooth_number, tooth_type="permanent"):
        super().__init__()
        self.tooth_number = tooth_number
        self.tooth_type = tooth_type  # permanent أو deciduous
        self.condition = "healthy"
        self.notes = ""
        
        self.setFixedSize(40, 40)
        self.setText(str(tooth_number))
        self.setToolTip(f"السن رقم {tooth_number}")
        
        self.update_appearance()
        self.clicked.connect(self.on_tooth_clicked)
        
    def update_appearance(self):
        """تحديث مظهر السن حسب حالته"""
        color = get_tooth_color(self.condition)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: 2px solid {COLORS['border']};
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                border-color: {COLORS['primary']};
                transform: scale(1.1);
            }}
            QPushButton:pressed {{
                transform: scale(0.95);
            }}
        """)
        
    def set_condition(self, condition, notes=""):
        """تعيين حالة السن"""
        self.condition = condition
        self.notes = notes
        self.update_appearance()
        
        # تحديث التلميح
        condition_names = {
            'healthy': 'سليم',
            'cavity': 'تسوس',
            'filled': 'حشوة',
            'crown': 'تاج',
            'missing': 'مفقود',
            'root_canal': 'علاج عصب',
            'extraction': 'خلع'
        }
        
        condition_text = condition_names.get(condition, condition)
        tooltip = f"السن رقم {self.tooth_number}\nالحالة: {condition_text}"
        if notes:
            tooltip += f"\nملاحظات: {notes}"
        self.setToolTip(tooltip)
        
    def on_tooth_clicked(self):
        """عند النقر على السن"""
        self.tooth_clicked.emit(str(self.tooth_number), self.tooth_type)

class ToothChart(QWidget):
    """خريطة الأسنان الكاملة"""
    
    def __init__(self, chart_type="permanent"):
        super().__init__()
        self.chart_type = chart_type  # permanent أو deciduous
        self.teeth = {}
        self.init_ui()
        
    def init_ui(self):
        """إنشاء واجهة خريطة الأسنان"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # عنوان الخريطة
        title = QLabel("خريطة الأسنان - نظام FDI")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title)
        
        # نوع الخريطة
        chart_type_label = QLabel("الأسنان الدائمة" if self.chart_type == "permanent" else "الأسنان اللبنية")
        chart_type_label.setAlignment(Qt.AlignCenter)
        chart_type_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_secondary']};
                font-size: 14px;
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(chart_type_label)
        
        # إنشاء الخريطة
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setSpacing(10)
        
        if self.chart_type == "permanent":
            self.create_permanent_teeth_chart(chart_layout)
        else:
            self.create_deciduous_teeth_chart(chart_layout)
            
        layout.addWidget(chart_widget)
        
        # مفتاح الألوان
    # self.create_color_legend(layout)  # تم التعطيل مؤقتاً لحل مشكلة QApplication
        
    def create_permanent_teeth_chart(self, layout):
        """إنشاء خريطة الأسنان الدائمة"""
        
        # الفك العلوي
        upper_layout = QHBoxLayout()
        upper_layout.setAlignment(Qt.AlignCenter)
        
        # الربع الثاني (21-28)
        upper_left = QHBoxLayout()
        for i in range(21, 29):
            tooth = ToothButton(i, "permanent")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            upper_left.addWidget(tooth)
        
        # الربع الأول (11-18)
        upper_right = QHBoxLayout()
        for i in range(18, 10, -1):
            tooth = ToothButton(i, "permanent")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            upper_right.addWidget(tooth)
            
        upper_layout.addLayout(upper_left)
        upper_layout.addSpacing(20)
        upper_layout.addLayout(upper_right)
        
        layout.addLayout(upper_layout)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet(f"background-color: {COLORS['border']};")
        layout.addWidget(line)
        
        # الفك السفلي
        lower_layout = QHBoxLayout()
        lower_layout.setAlignment(Qt.AlignCenter)
        
        # الربع الثالث (31-38)
        lower_left = QHBoxLayout()
        for i in range(31, 39):
            tooth = ToothButton(i, "permanent")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            lower_left.addWidget(tooth)
        
        # الربع الرابع (41-48)
        lower_right = QHBoxLayout()
        for i in range(48, 40, -1):
            tooth = ToothButton(i, "permanent")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            lower_right.addWidget(tooth)
            
        lower_layout.addLayout(lower_left)
        lower_layout.addSpacing(20)
        lower_layout.addLayout(lower_right)
        
        layout.addLayout(lower_layout)
        
    def create_deciduous_teeth_chart(self, layout):
        """إنشاء خريطة الأسنان اللبنية"""
        
        # الفك العلوي
        upper_layout = QHBoxLayout()
        upper_layout.setAlignment(Qt.AlignCenter)
        
        # الربع الثاني (61-65)
        upper_left = QHBoxLayout()
        for i in range(61, 66):
            tooth = ToothButton(i, "deciduous")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            upper_left.addWidget(tooth)
        
        # الربع الأول (51-55)
        upper_right = QHBoxLayout()
        for i in range(55, 50, -1):
            tooth = ToothButton(i, "deciduous")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            upper_right.addWidget(tooth)
            
        upper_layout.addLayout(upper_left)
        upper_layout.addSpacing(20)
        upper_layout.addLayout(upper_right)
        
        layout.addLayout(upper_layout)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet(f"background-color: {COLORS['border']};")
        layout.addWidget(line)
        
        # الفك السفلي
        lower_layout = QHBoxLayout()
        lower_layout.setAlignment(Qt.AlignCenter)
        
        # الربع الثالث (71-75)
        lower_left = QHBoxLayout()
        for i in range(71, 76):
            tooth = ToothButton(i, "deciduous")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            lower_left.addWidget(tooth)
        
        # الربع الرابع (81-85)
        lower_right = QHBoxLayout()
        for i in range(85, 80, -1):
            tooth = ToothButton(i, "deciduous")
            tooth.tooth_clicked.connect(self.on_tooth_selected)
            self.teeth[str(i)] = tooth
            lower_right.addWidget(tooth)
            
        lower_layout.addLayout(lower_left)
        lower_layout.addSpacing(20)
        lower_layout.addLayout(lower_right)
        
        layout.addLayout(lower_layout)
        
    def create_color_legend(self, layout):
        """إنشاء مفتاح الألوان"""
        legend_frame = QFrame()
        legend_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                padding: 15px;
                margin-top: 20px;
            }}
        """)
        
        legend_layout = QVBoxLayout(legend_frame)
        
        legend_title = QLabel("مفتاح الألوان:")
        legend_title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 10px;
            }}
        """)
        legend_layout.addWidget(legend_title)
        
        # إنشاء مفتاح الألوان
        colors_layout = QHBoxLayout()
        
        conditions = [
            ('healthy', 'سليم'),
            ('cavity', 'تسوس'),
            ('filled', 'حشوة'),
            ('crown', 'تاج'),
            ('missing', 'مفقود'),
            ('root_canal', 'علاج عصب'),
            ('extraction', 'خلع')
        ]
        
        for condition, name in conditions:
            color_item = QHBoxLayout()
            
            # مربع اللون
            color_box = QLabel()
            color_box.setFixedSize(20, 20)
            color_box.setStyleSheet(f"""
                QLabel {{
                    background-color: {get_tooth_color(condition)};
                    border: 1px solid {COLORS['border']};
                    border-radius: 3px;
                }}
            """)
            
            # النص
            color_text = QLabel(name)
            color_text.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['text_primary']};
                    font-size: 12px;
                    margin-left: 5px;
                }}
            """)
            
            color_item.addWidget(color_box)
            color_item.addWidget(color_text)
            colors_layout.addLayout(color_item)
            
        colors_layout.addStretch()
        legend_layout.addLayout(colors_layout)
        layout.addWidget(legend_frame)
        
    def on_tooth_selected(self, tooth_number, tooth_type):
        """عند اختيار سن"""
        # يمكن إضافة نافذة لتعديل حالة السن
        self.show_tooth_dialog(tooth_number, tooth_type)
        
    def show_tooth_dialog(self, tooth_number, tooth_type):
        """عرض نافذة تعديل حالة السن"""
        dialog = ToothConditionDialog(tooth_number, tooth_type, self)
        if dialog.exec_() == QDialog.Accepted:
            condition, notes = dialog.get_condition()
            self.teeth[tooth_number].set_condition(condition, notes)
            
    def get_teeth_data(self):
        """الحصول على بيانات جميع الأسنان"""
        teeth_data = {}
        for tooth_number, tooth_button in self.teeth.items():
            teeth_data[tooth_number] = {
                'condition': tooth_button.condition,
                'notes': tooth_button.notes,
                'type': tooth_button.tooth_type
            }
        return teeth_data
        
    def load_teeth_data(self, teeth_data):
        """تحميل بيانات الأسنان"""
        for tooth_number, data in teeth_data.items():
            if tooth_number in self.teeth:
                self.teeth[tooth_number].set_condition(
                    data.get('condition', 'healthy'),
                    data.get('notes', '')
                )

class ToothConditionDialog(QDialog):
    """نافذة تعديل حالة السن"""
    
    def __init__(self, tooth_number, tooth_type, parent=None):
        super().__init__(parent)
        self.tooth_number = tooth_number
        self.tooth_type = tooth_type
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle(f"تعديل حالة السن رقم {self.tooth_number}")
        self.resize(600, 420)

        layout = QVBoxLayout(self)
        layout.setSpacing(18)

        # عنوان
        title = QLabel(f"السن رقم {self.tooth_number}")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 24px;
            }}
        """)
        layout.addWidget(title)

        # اختيار الحالة
        condition_label = QLabel("حالة السن:")
        condition_label.setStyleSheet(f"font-size: 18px; font-weight: 600; color: {COLORS['text_primary']};")
        layout.addWidget(condition_label)

        self.condition_combo = QComboBox()
        self.condition_combo.setStyleSheet("font-size: 18px; padding: 6px 12px;")
        self.condition_combo.addItems([
            "سليم", "تسوس", "حشوة", "تاج", "مفقود", "علاج عصب", "خلع"
        ])
        self.condition_combo.setMinimumHeight(38)
        layout.addWidget(self.condition_combo)

        # ملاحظات
        notes_label = QLabel("ملاحظات:")
        notes_label.setStyleSheet(f"font-size: 18px; font-weight: 600; color: {COLORS['text_primary']}; margin-top: 10px;")
        layout.addWidget(notes_label)

        self.notes_text = QTextEdit()
        self.notes_text.setMinimumHeight(120)
        self.notes_text.setStyleSheet("font-size: 17px; padding: 8px;")
        layout.addWidget(self.notes_text)

        # أزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        save_btn = QPushButton("حفظ")
        save_btn.setMinimumHeight(38)
        save_btn.setMinimumWidth(120)
        save_btn.setStyleSheet(f"font-size: 18px; font-weight: bold; background: {COLORS['primary']}; color: white; border-radius: 8px;")
        save_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setMinimumHeight(38)
        cancel_btn.setMinimumWidth(120)
        cancel_btn.setStyleSheet(f"font-size: 18px; font-weight: bold; background: #eee; color: #444; border-radius: 8px;")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        
    def get_condition(self):
        """الحصول على الحالة المختارة"""
        condition_map = {
            "سليم": "healthy",
            "تسوس": "cavity",
            "حشوة": "filled",
            "تاج": "crown",
            "مفقود": "missing",
            "علاج عصب": "root_canal",
            "خلع": "extraction"
        }
        
        condition_text = self.condition_combo.currentText()
        condition = condition_map.get(condition_text, "healthy")
        notes = self.notes_text.toPlainText()
        
        return condition, notes
