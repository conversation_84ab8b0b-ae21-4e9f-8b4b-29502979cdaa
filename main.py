#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة عيادة الأسنان
عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان

الملف الرئيسي لتشغيل التطبيق
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QTranslator, QLocale
from PyQt5.QtGui import QFont, QIcon

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from login_window import LoginWindow
from main_window import MainWindow
from database import DatabaseManager
from cairo_fonts import font_manager
from arabic_settings import setup_arabic_application

class ClinicApp:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_application()
        self.db = DatabaseManager()
        
    def setup_application(self):
        """إعداد التطبيق الأساسي مع دعم خط Cairo والاتجاه العربي"""

        # تعيين اسم التطبيق
        self.app.setApplicationName("نظام إدارة عيادة الأسنان")
        self.app.setApplicationVersion("1.0")
        self.app.setOrganizationName("عيادة الدكتورة ضياء أبو جلبان")

        # ضبط ترميز UTF-8 ومتغيرات البيئة
        import locale
        import os

        # ضبط متغيرات البيئة للعربية
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['LANG'] = 'ar_SA.UTF-8'
        os.environ['LC_ALL'] = 'ar_SA.UTF-8'
        os.environ['LC_CTYPE'] = 'ar_SA.UTF-8'

        # ضبط locale للعربية مع خيارات متعددة
        locales_to_try = [
            'ar_SA.UTF-8',
            'Arabic_Saudi Arabia.1256',
            'ar_SA',
            'ar',
            'en_US.UTF-8'
        ]

        for loc in locales_to_try:
            try:
                locale.setlocale(locale.LC_ALL, loc)
                print(f"✅ تم ضبط locale إلى: {loc}")
                break
            except:
                continue
        else:
            print("⚠️ استخدام locale الافتراضي")

        # ضبط ترميز stdout إذا كان متاحاً
        if hasattr(sys, 'stdout') and hasattr(sys.stdout, 'reconfigure'):
            try:
                sys.stdout.reconfigure(encoding='utf-8')
            except:
                pass

        # إعداد شامل للغة العربية
        print("🔧 بدء إعداد اللغة العربية...")
        arabic_result = setup_arabic_application(self.app)

        # إعداد إضافي بخط Cairo
        font_manager.setup_application_rtl(self.app)

        # طباعة نتائج الإعداد
        print("📊 نتائج إعداد العربية:")
        for key, value in arabic_result.items():
            print(f"   {key}: {value}")

        # تحديث متغير الخط في ملف الأنماط
        import styles
        styles.ARABIC_FONT = font_manager.best_font

        # تحسين جودة النص والعرض
        self.app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        self.app.setAttribute(Qt.AA_EnableHighDpiScaling, True)

        # تعيين أيقونة التطبيق (يمكن إضافة ملف أيقونة لاحقاً)
        # self.app.setWindowIcon(QIcon("icon.ico"))

        print("✅ تم إعداد التطبيق بخط Cairo والاتجاه العربي")
        
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow()
        
        # إذا تم تسجيل الدخول بنجاح
        if login_window.exec_() == login_window.Accepted:
            return True
        else:
            return False
            
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.main_window = MainWindow()
        self.main_window.show()
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            # عرض نافذة تسجيل الدخول
            if self.show_login():
                # إذا تم تسجيل الدخول بنجاح، عرض النافذة الرئيسية
                self.show_main_window()
                
                # تشغيل حلقة الأحداث
                return self.app.exec_()
            else:
                # إذا تم إلغاء تسجيل الدخول
                return 0
                
        except Exception as e:
            # في حالة حدوث خطأ
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("خطأ في التطبيق")
            error_msg.setText(f"حدث خطأ غير متوقع:\n{str(e)}")
            error_msg.setDetailedText(f"تفاصيل الخطأ:\n{str(e)}")
            error_msg.exec_()
            return 1

def main():
    """الدالة الرئيسية"""
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 6):
        print("يتطلب هذا التطبيق Python 3.6 أو أحدث")
        sys.exit(1)
    
    # التحقق من وجود PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
    except ImportError:
        print("PyQt5 غير مثبت. يرجى تثبيته باستخدام: pip install PyQt5")
        sys.exit(1)
    
    # إنشاء وتشغيل التطبيق
    clinic_app = ClinicApp()
    exit_code = clinic_app.run()
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
