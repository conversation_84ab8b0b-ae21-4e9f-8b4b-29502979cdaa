#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
تكوين الخطوط العربية للتطبيق
تم إنشاؤه تلقائياً
'''

# الخط المختار
SELECTED_FONT = "Segoe UI"

# قائمة الخطوط المتاحة
AVAILABLE_FONTS = Segoe UI

# إعدادات الخط
FONT_CONFIG = {
    'name': SELECTED_FONT,
    'size_small': 10,
    'size_normal': 12,
    'size_medium': 14,
    'size_large': 16,
    'size_header': 18,
    'size_title': 24
}

def get_font_name():
    return SELECTED_FONT

def get_font_config():
    return FONT_CONFIG
