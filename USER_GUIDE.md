# دليل المستخدم - نظام إدارة عيادة الأسنان

## 🦷 عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان

---

## 📖 فهرس المحتويات

1. [بدء الاستخدام](#بدء-الاستخدام)
2. [تسجيل الدخول](#تسجيل-الدخول)
3. [الواجهة الرئيسية](#الواجهة-الرئيسية)
4. [إدارة المرضى](#إدارة-المرضى)
5. [مخطط الأسنان](#مخطط-الأسنان)
6. [نظام الخطوط](#نظام-الخطوط)
7. [حل المشاكل](#حل-المشاكل)

---

## 🚀 بدء الاستخدام

### متطلبات النظام
- Windows 10 أو أحدث
- Python 3.6 أو أحدث
- ذاكرة وصول عشوائي: 4 جيجابايت على الأقل
- مساحة تخزين: 500 ميجابايت

### التشغيل السريع
1. انقر نقراً مزدوجاً على ملف `run_clinic.bat`
2. أو افتح موجه الأوامر واكتب: `python main.py`

---

## 🔐 تسجيل الدخول

### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `123456`

### خطوات تسجيل الدخول
1. أدخل اسم المستخدم في الحقل الأول
2. أدخل كلمة المرور في الحقل الثاني
3. انقر على زر "تسجيل الدخول"

> ⚠️ **تنبيه أمني**: يُنصح بتغيير كلمة المرور الافتراضية فور أول استخدام

---

## 🏠 الواجهة الرئيسية

### الشريط الجانبي
يحتوي على الأقسام التالية:
- **📊 لوحة التحكم**: عرض الإحصائيات العامة
- **👥 المرضى**: إدارة بيانات المرضى
- **📅 المواعيد**: جدولة المواعيد (قيد التطوير)
- **💊 العلاجات**: تسجيل العلاجات (قيد التطوير)
- **💰 الفواتير**: إدارة الفواتير (قيد التطوير)

### لوحة التحكم
تعرض إحصائيات سريعة:
- عدد المرضى المسجلين
- عدد المواعيد اليوم
- عدد العلاجات المكتملة
- إجمالي الفواتير

---

## 👥 إدارة المرضى

### إضافة مريض جديد
1. انقر على تبويب "المرضى"
2. انقر على زر "إضافة مريض جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - رقم الهاتف
   - العنوان
   - تاريخ الميلاد
   - الجنس
4. يمكن رفع صورة شخصية (اختياري)
5. انقر على "حفظ"

### تعديل بيانات مريض
1. ابحث عن المريض في الجدول
2. انقر نقراً مزدوجاً على صف المريض
3. أو انقر على زر "تعديل"
4. قم بتعديل البيانات المطلوبة
5. انقر على "حفظ التغييرات"

### البحث والفلترة
- استخدم مربع البحث للبحث بالاسم أو رقم الهاتف
- انقر على عناوين الأعمدة للترتيب
- استخدم الفلاتر المتقدمة للبحث المفصل

---

## 🦷 مخطط الأسنان

### نظام الترقيم FDI
النظام يستخدم ترقيم FDI الدولي:

#### الأسنان الدائمة
- **الربع الأول**: 11-18 (أعلى يمين)
- **الربع الثاني**: 21-28 (أعلى يسار)
- **الربع الثالث**: 31-38 (أسفل يسار)
- **الربع الرابع**: 41-48 (أسفل يمين)

#### الأسنان اللبنية
- **الربع الأول**: 51-55 (أعلى يمين)
- **الربع الثاني**: 61-65 (أعلى يسار)
- **الربع الثالث**: 71-75 (أسفل يسار)
- **الربع الرابع**: 81-85 (أسفل يمين)

### حالات الأسنان والألوان
- 🟢 **سليم** (أخضر): سن صحي بدون مشاكل
- 🔴 **تسوس** (أحمر): يحتاج علاج
- 🔵 **حشوة** (أزرق): تم علاجه بحشوة
- 🟡 **تاج** (ذهبي): تم تركيب تاج
- ⚫ **مفقود** (رمادي): سن مفقود
- 🟣 **علاج عصب** (بنفسجي): تم علاج العصب
- ⚫ **خلع** (أسود): تم خلع السن

### استخدام مخطط الأسنان
1. انقر على أي سن في المخطط
2. ستظهر نافذة تحديد حالة السن
3. اختر الحالة المناسبة من القائمة
4. أضف ملاحظات إضافية إذا لزم الأمر
5. انقر على "حفظ"

---

## 🔤 نظام الخطوط المحسن

### الخطوط المدعومة
النظام يختار تلقائياً أفضل خط عربي متاح:

#### Windows
- Segoe UI (الأفضل)
- Tahoma
- Arial Unicode MS
- Microsoft Sans Serif

#### macOS
- SF Pro Text (الأفضل)
- Helvetica Neue
- Arial Unicode MS

#### Linux
- Noto Sans Arabic (الأفضل)
- DejaVu Sans
- Liberation Sans

### اختبار الخطوط
لاختبار جودة الخط المستخدم:
```bash
python font_test.py
```

### تحسين جودة النص
- النظام يطبق تلقائياً تحسينات لجودة النص العربي
- يدعم أحجام خطوط مختلفة للعناوين والنصوص
- تحسين خاص للنصوص في الجداول والأزرار

---

## 🔧 حل المشاكل

### مشاكل شائعة وحلولها

#### التطبيق لا يبدأ
```bash
# تحقق من تثبيت Python
python --version

# تحقق من المتطلبات
pip install -r requirements.txt

# تشغيل مباشر
python main.py
```

#### مشاكل الخطوط
```bash
# اختبار الخطوط
python font_test.py

# في حالة ظهور خطوط غير واضحة، جرب:
# 1. إعادة تشغيل التطبيق
# 2. تحديث نظام التشغيل
# 3. تثبيت خطوط عربية إضافية
```

#### مشاكل قاعدة البيانات
```bash
# في حالة تلف قاعدة البيانات
# احذف ملف clinic.db وأعد تشغيل التطبيق
# سيتم إنشاء قاعدة بيانات جديدة تلقائياً
```

### رسائل الخطأ الشائعة

#### "QFontDatabase: Must construct a QGuiApplication"
- هذا خطأ مؤقت عند بدء التطبيق
- عادة ما يختفي تلقائياً
- إذا استمر، أعد تشغيل التطبيق

#### "Unknown property transform/box-shadow"
- هذه تحذيرات طبيعية من PyQt5
- لا تؤثر على عمل التطبيق
- يمكن تجاهلها بأمان

---

## 📞 الدعم الفني

### للحصول على المساعدة
- راجع هذا الدليل أولاً
- جرب حلول المشاكل الشائعة
- تأكد من تحديث النظام

### معلومات النظام المفيدة
عند طلب الدعم، يرجى تقديم:
- نظام التشغيل وإصداره
- إصدار Python المستخدم
- رسالة الخطأ كاملة (إن وجدت)
- خطوات إعادة إنتاج المشكلة

---

**تم إعداد هذا الدليل بـ ❤️ لمساعدتكم في استخدام النظام بكفاءة**
