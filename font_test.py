#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وتشخيص الخطوط العربية
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from cairo_fonts import font_manager, get_font_info

class FontTestWindow(QWidget):
    """نافذة اختبار الخطوط"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار الخطوط العربية - نظام إدارة عيادة الأسنان")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # معلومات النظام
        info = get_font_info()
        
        # عنوان
        title = QLabel("🔤 اختبار الخطوط العربية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2563eb;
                margin: 20px;
                padding: 10px;
                background-color: #f8fafc;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الخط المكتشف
        font_info = QLabel(f"""
📋 معلومات الخط المكتشف:
• اسم الخط: {info['name']}
• نظام التشغيل: {info['system']}
• يدعم العربية: {'نعم' if info['supports_arabic'] else 'لا'}
        """)
        font_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                padding: 15px;
                background-color: #e0f2fe;
                border-radius: 8px;
                border-left: 4px solid #0284c7;
            }
        """)
        layout.addWidget(font_info)
        
        # نص تجريبي بأحجام مختلفة
        test_texts = [
            ("عنوان كبير", 24, True),
            ("عنوان فرعي متوسط", 18, True),
            ("نص عادي للقراءة والكتابة", 14, False),
            ("نص صغير للملاحظات", 12, False),
        ]
        
        for text, size, bold in test_texts:
            label = QLabel(f"🦷 {text} - عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان")
            font = font_manager.get_font(size, QFont.Bold if bold else QFont.Normal)
            label.setFont(font)
            label.setStyleSheet(f"""
                QLabel {{
                    padding: 10px;
                    margin: 5px;
                    background-color: white;
                    border: 1px solid #e2e8f0;
                    border-radius: 6px;
                }}
            """)
            layout.addWidget(label)
        
        # منطقة نص تفاعلية
        text_area_label = QLabel("📝 منطقة اختبار الكتابة:")
        text_area_label.setStyleSheet("font-weight: bold; margin-top: 20px;")
        layout.addWidget(text_area_label)
        
        text_area = QTextEdit()
        text_area.setPlainText("""
مرحباً بكم في نظام إدارة عيادة الأسنان

هذا النص مكتوب باللغة العربية لاختبار جودة الخط المستخدم.
يمكنكم الكتابة هنا لاختبار الخط بأنفسكم.

الأرقام: ١٢٣٤٥٦٧٨٩٠
الأرقام الإنجليزية: 1234567890

أسماء الأسنان:
- القواطع المركزية
- القواطع الجانبية  
- الأنياب
- الضواحك الأولى والثانية
- الأرحاء الأولى والثانية والثالثة

حالات الأسنان:
✅ سليم
❌ تسوس
🔵 حشوة
👑 تاج
⚫ مفقود
🟣 علاج عصب
        """)
        
        font = font_manager.get_body_font(14)
        text_area.setFont(font)
        text_area.setStyleSheet("""
            QTextEdit {
                padding: 15px;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                background-color: white;
                line-height: 1.6;
            }
            QTextEdit:focus {
                border-color: #2563eb;
            }
        """)
        layout.addWidget(text_area)
        
        # ملاحظة
        note = QLabel("💡 ملاحظة: يمكنكم الكتابة في المنطقة أعلاه لاختبار جودة الخط")
        note.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #64748b;
                font-style: italic;
                padding: 10px;
            }
        """)
        layout.addWidget(note)
        
        self.setLayout(layout)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق الخط العربي على التطبيق
    font = font_manager.get_body_font(12)
    app.setFont(font)
    
    window = FontTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    # main()  # تم التعطيل مؤقتاً لمنع تنفيذ كود واجهة يسبب خطأ QApplication
