#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول عصرية
عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from styles import ARABIC_FONT

class LoginWindow(QDialog):

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def login(self):
        # يمكنك إضافة تحقق فعلي هنا لاحقًا
        QMessageBox.information(self, "تم تسجيل الدخول", "مرحبًا بك في النظام!")
        self.accept()  # إغلاق النافذة مع قبول تسجيل الدخول

    def init_ui(self):
        self.setWindowTitle("تسجيل الدخول - عيادة الأسنان")
        self.setFixedSize(480, 420)
        self.setStyleSheet("background: #f1f5f9;")
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_layout.addStretch(1)

        login_card = QFrame()
        login_card.setObjectName("login_card")
        login_card.setStyleSheet("""
QFrame {
    background: #fff;
    border-radius: 22px;
    border: 1.5px solid #e0e7ef;
    min-width: 400px;
    max-width: 400px;
}
        """)
        card_layout = QVBoxLayout(login_card)
        card_layout.setContentsMargins(32, 32, 32, 32)
        card_layout.setSpacing(0)

        title = QLabel("مرحبا بك 👋")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 28px; font-weight: bold; color: #2563eb; margin-bottom: 8px;")
        card_layout.addWidget(title)

        subtitle = QLabel("يرجى تسجيل الدخول للمتابعة")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("font-size: 16px; color: #64748b; margin-bottom: 18px;")
        card_layout.addWidget(subtitle)
        card_layout.addSpacing(18)

        # حقل اسم المستخدم مع أيقونة داخلية
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        self.username_input.setFixedHeight(42)
        self.username_input.setMinimumWidth(260)
        self.username_input.setMaximumWidth(260)
        self.username_input.setStyleSheet(
            """
QLineEdit {
    font-family: 'Segoe UI', 'Cairo';
    font-size: 17px;
    padding-right: 36px;
    padding-left: 12px;
    border: 1.5px solid #bfc9d1;
    border-radius: 10px;
    background: #f7fafc;
    color: #22223b;
    letter-spacing: 0.2px;
}
QLineEdit:focus {
    border: 1.5px solid #2563eb;
    background: #fff;
}
            """
        )
        user_icon_action = QAction("👤", self.username_input)
        self.username_input.addAction(user_icon_action, QLineEdit.TrailingPosition)
        card_layout.addWidget(self.username_input, alignment=Qt.AlignCenter)
        card_layout.addSpacing(12)

        # حقل كلمة المرور مع أيقونة داخلية
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(42)
        self.password_input.setMinimumWidth(260)
        self.password_input.setMaximumWidth(260)
        self.password_input.setStyleSheet(
            """
QLineEdit {
    font-family: 'Segoe UI', 'Cairo';
    font-size: 17px;
    padding-right: 36px;
    padding-left: 12px;
    border: 1.5px solid #bfc9d1;
    border-radius: 10px;
    background: #f7fafc;
    color: #22223b;
    letter-spacing: 0.2px;
}
QLineEdit:focus {
    border: 1.5px solid #2563eb;
    background: #fff;
}
            """
        )
        pass_icon_action = QAction("🔒", self.password_input)
        self.password_input.addAction(pass_icon_action, QLineEdit.TrailingPosition)
        card_layout.addWidget(self.password_input, alignment=Qt.AlignCenter)
        card_layout.addSpacing(18)

        self.login_btn = QPushButton("تسجيل الدخول  🚀")
        self.login_btn.setFixedHeight(44)
        self.login_btn.setMinimumWidth(180)
        self.login_btn.setMaximumWidth(260)
        self.login_btn.setStyleSheet(
            """
QPushButton {
    font-family: 'Segoe UI', 'Cairo';
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2563eb, stop:1 #1e40af);
    border: none;
    border-radius: 10px;
    min-height: 44px;
    letter-spacing: 0.5px;
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1e40af, stop:1 #2563eb);
}
            """
        )
        card_layout.addSpacing(6)
        card_layout.addWidget(self.login_btn, alignment=Qt.AlignCenter)

        self.close_btn = QPushButton("إغلاق  ❌")
        self.close_btn.setFixedHeight(40)
        self.close_btn.setMinimumWidth(120)
        self.close_btn.setMaximumWidth(180)
        self.close_btn.setStyleSheet(
            """
QPushButton {
    font-family: 'Segoe UI', 'Cairo';
    font-size: 15px;
    font-weight: 500;
    color: #fff;
    background: #e5e7eb;
    border: none;
    border-radius: 10px;
    min-height: 40px;
    letter-spacing: 0.5px;
}
QPushButton:hover {
    background: #ef4444;
    color: #fff;
}
            """
        )
        card_layout.addWidget(self.close_btn, alignment=Qt.AlignCenter)
        card_layout.addStretch(1)

        main_layout.addWidget(login_card, alignment=Qt.AlignCenter)
        main_layout.addStretch(1)

        font = QFont("Segoe UI", 13)
        self.setFont(font)
        self.login_btn.clicked.connect(self.login)
        self.close_btn.clicked.connect(self.close)
    # تم حذف أي نص ثلاثي أو تكرار زائد بعد نهاية الدالة
