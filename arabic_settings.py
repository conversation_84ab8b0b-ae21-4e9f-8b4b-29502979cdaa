#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات اللغة العربية المحسنة للتطبيق
"""

import sys
import os
import locale
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QFontDatabase

class ArabicSettings:
    """فئة إعدادات اللغة العربية"""
    
    def __init__(self):
        self.encoding_fixed = False
        self.locale_set = False
        self.font_configured = False
        self.best_font = None
        
    def fix_encoding(self):
        """إصلاح مشاكل الترميز"""
        
        try:
            # ضبط متغيرات البيئة
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['LANG'] = 'ar_SA.UTF-8'
            os.environ['LC_ALL'] = 'ar_SA.UTF-8'
            os.environ['LC_CTYPE'] = 'ar_SA.UTF-8'
            
            # ضبط ترميز Python
            if hasattr(sys, 'setdefaultencoding'):
                sys.setdefaultencoding('utf-8')
            
            # ضبط ترميز stdout
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            
            self.encoding_fixed = True
            print("✅ تم إصلاح ترميز UTF-8")
            return True
            
        except Exception as e:
            print(f"⚠️ تحذير في إصلاح الترميز: {e}")
            return False
    
    def set_arabic_locale(self):
        """ضبط locale للغة العربية"""
        
        locales_to_try = [
            'ar_SA.UTF-8',
            'Arabic_Saudi Arabia.1256',
            'ar_SA.utf8',
            'ar_SA',
            'ar',
            'en_US.UTF-8',
            'C.UTF-8'
        ]
        
        for loc in locales_to_try:
            try:
                locale.setlocale(locale.LC_ALL, loc)
                print(f"✅ تم ضبط locale: {loc}")
                self.locale_set = True
                return loc
            except:
                continue
        
        print("⚠️ لم يتم العثور على locale مناسب")
        return None
    
    def get_best_arabic_font(self):
        """الحصول على أفضل خط عربي متاح"""
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        font_db = QFontDatabase()
        available_families = font_db.families()
        
        # قائمة الخطوط مرتبة حسب الأولوية
        priority_fonts = [
            'Segoe UI',           # الأفضل لـ Windows
            'Tahoma',             # خط موثوق
            'Arial Unicode MS',   # دعم شامل
            'Microsoft Sans Serif', # خط النظام
            'Times New Roman',    # خط كلاسيكي
            'Arial',              # خط عام
            'Calibri',            # خط حديث
            'Verdana'             # خط واضح
        ]
        
        for font_name in priority_fonts:
            if font_name in available_families:
                # اختبار جودة الخط مع النص العربي
                if self.test_font_quality(font_name):
                    self.best_font = font_name
                    self.font_configured = True
                    print(f"✅ تم اختيار الخط: {font_name}")
                    return font_name
        
        # إذا لم يتم العثور على خط مناسب
        print("⚠️ لم يتم العثور على خط عربي مناسب")
        return 'Arial'  # خط افتراضي
    
    def test_font_quality(self, font_name):
        """اختبار جودة الخط مع النص العربي"""
        
        try:
            font = QFont(font_name, 12)
            
            # اختبار النص العربي
            test_text = "مرحباً بكم في عيادة الأسنان"
            
            # التحقق من دعم الخط للعربية
            font_metrics = font.pointSize()
            if font_metrics > 0:
                return True
            
        except:
            pass
        
        return False
    
    def configure_application(self, app):
        """ضبط التطبيق للغة العربية"""
        
        # إصلاح الترميز
        self.fix_encoding()
        
        # ضبط locale
        self.set_arabic_locale()
        
        # ضبط الاتجاه
        app.setLayoutDirection(Qt.RightToLeft)
        
        # ضبط الخط
        best_font = self.get_best_arabic_font()
        if best_font:
            font = QFont(best_font, 12)
            font.setStyleHint(QFont.System)
            font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
            app.setFont(font)
        
        # تحسين العرض
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)
        
        # اختبار النص العربي
        self.test_arabic_display()
        
        return {
            'encoding_fixed': self.encoding_fixed,
            'locale_set': self.locale_set,
            'font_configured': self.font_configured,
            'best_font': self.best_font
        }
    
    def test_arabic_display(self):
        """اختبار عرض النص العربي"""
        
        test_texts = [
            "عيادة الدكتورة ضياء أبو جلبان",
            "إدارة المرضى والمواعيد",
            "الأرقام: ١٢٣٤٥٦٧٨٩٠"
        ]
        
        print("🧪 اختبار عرض النص العربي:")
        for text in test_texts:
            try:
                encoded = text.encode('utf-8').decode('utf-8')
                print(f"   ✅ {text}")
            except:
                print(f"   ❌ خطأ في: {text}")
    
    def get_status_report(self):
        """تقرير حالة الإعدادات العربية"""
        
        report = {
            'encoding': '✅ UTF-8' if self.encoding_fixed else '❌ خطأ',
            'locale': '✅ عربي' if self.locale_set else '❌ غير مضبوط',
            'font': f'✅ {self.best_font}' if self.font_configured else '❌ غير مضبوط',
            'overall': '✅ جاهز' if all([self.encoding_fixed, self.locale_set, self.font_configured]) else '⚠️ يحتاج إصلاح'
        }
        
        return report

# إنشاء مثيل عام
arabic_settings = ArabicSettings()

def setup_arabic_application(app):
    """دالة سريعة لإعداد التطبيق للعربية"""
    return arabic_settings.configure_application(app)

def get_arabic_status():
    """الحصول على حالة الإعدادات العربية"""
    return arabic_settings.get_status_report()

def test_arabic_setup():
    """اختبار إعداد اللغة العربية"""
    
    print("🔧 اختبار إعداد اللغة العربية")
    print("=" * 40)
    
    # إنشاء تطبيق تجريبي
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # إعداد العربية
    result = setup_arabic_application(app)
    
    # طباعة النتائج
    print("\n📊 نتائج الإعداد:")
    for key, value in result.items():
        print(f"   {key}: {value}")
    
    # تقرير الحالة
    status = get_arabic_status()
    print("\n📋 تقرير الحالة:")
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    return result

if __name__ == "__main__":
    test_arabic_setup()
