#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية مع دعم خط Cairo والاتجاه العربي
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from cairo_fonts import font_manager, setup_widget_rtl
from styles import COLORS, BUTTON_STYLES, MAIN_STYLE, TABLE_STYLES
from database import DatabaseManager
from patient_dialog import PatientDialog

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("نظام إدارة عيادة الدكتورة ضياء أبو جلبان")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # ضبط الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق النمط الرئيسي
        self.setStyleSheet(MAIN_STYLE + TABLE_STYLES)
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # تطبيق الخط العربي
        font = QFont("Arial", 10)
        font.setFamily("Segoe UI")
        self.setFont(font)
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_patient_action = QAction("مريض جديد", self)
        new_patient_action.setShortcut("Ctrl+N")
        new_patient_action.triggered.connect(self.show_patients_tab)
        file_menu.addAction(new_patient_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخ احتياطي", self)
        backup_action.triggered.connect(self.backup_database)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        patients_action = QAction("المرضى", self)
        patients_action.triggered.connect(self.show_patients_tab)
        view_menu.addAction(patients_action)
        
        appointments_action = QAction("المواعيد", self)
        appointments_action.triggered.connect(self.show_appointments_tab)
        view_menu.addAction(appointments_action)
        
        treatments_action = QAction("العلاجات", self)
        treatments_action.triggered.connect(self.show_treatments_tab)
        view_menu.addAction(treatments_action)
        
        invoices_action = QAction("الفواتير", self)
        invoices_action.triggered.connect(self.show_invoices_tab)
        view_menu.addAction(invoices_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setLayoutDirection(Qt.RightToLeft)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر مريض جديد
        new_patient_btn = QAction("👤 مريض جديد", self)
        new_patient_btn.triggered.connect(self.show_patients_tab)
        toolbar.addAction(new_patient_btn)
        
        toolbar.addSeparator()
        
        # زر موعد جديد
        new_appointment_btn = QAction("📅 موعد جديد", self)
        new_appointment_btn.triggered.connect(self.show_appointments_tab)
        toolbar.addAction(new_appointment_btn)
        
        toolbar.addSeparator()
        
        # زر العلاجات
        treatments_btn = QAction("🦷 العلاجات", self)
        treatments_btn.triggered.connect(self.show_treatments_tab)
        toolbar.addAction(treatments_btn)
        
        toolbar.addSeparator()
        
        # زر الفواتير
        invoices_btn = QAction("💰 الفواتير", self)
        invoices_btn.triggered.connect(self.show_invoices_tab)
        toolbar.addAction(invoices_btn)
        
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # الشريط الجانبي
        self.create_sidebar(main_layout)
        
        # المنطقة الرئيسية
        self.create_main_area(main_layout)
        
    def create_sidebar(self, parent_layout):
        """إنشاء الشريط الجانبي"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setSpacing(15)
        
        # شعار العيادة
        logo_label = QLabel("🦷")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet(f"""
            QLabel {{
                font-size: 40px;
                color: {COLORS['primary']};
                background-color: {COLORS['background']};
                border-radius: 30px;
                padding: 15px;
                margin-bottom: 10px;
            }}
        """)
        sidebar_layout.addWidget(logo_label)
        
        # اسم العيادة
        clinic_name = QLabel("عيادة د. ضياء أبو جلبان")
        clinic_name.setAlignment(Qt.AlignCenter)
        clinic_name.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        sidebar_layout.addWidget(clinic_name)
        
        # أزرار التنقل
        nav_buttons = [
            ("👥 إدارة المرضى", self.show_patients_tab),
            ("📅 المواعيد", self.show_appointments_tab),
            ("🦷 العلاجات", self.show_treatments_tab),
            ("💰 الفواتير", self.show_invoices_tab),
            ("📊 التقارير", self.show_reports_tab),
            ("⚙️ الإعدادات", self.show_settings_tab),
        ]
        
        for text, callback in nav_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: {COLORS['text_primary']};
                    border: none;
                    padding: 15px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    text-align: left;
                }}
                QPushButton:hover {{
                    background-color: {COLORS['hover']};
                    color: {COLORS['primary']};
                }}
                QPushButton:pressed {{
                    background-color: {COLORS['primary']};
                    color: white;
                }}
            """)
            btn.clicked.connect(callback)
            sidebar_layout.addWidget(btn)
        
        sidebar_layout.addStretch()
        
        # معلومات المستخدم
        user_info = QLabel("د. ضياء أبو جلبان\nطبيبة أسنان")
        user_info.setAlignment(Qt.AlignCenter)
        user_info.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_secondary']};
                font-size: 12px;
                padding: 10px;
                background-color: {COLORS['background']};
                border-radius: 8px;
            }}
        """)
        sidebar_layout.addWidget(user_info)
        
        parent_layout.addWidget(sidebar)
        
    def create_main_area(self, parent_layout):
        """إنشاء المنطقة الرئيسية"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {COLORS['border']};
                border-radius: 8px;
                background-color: {COLORS['surface']};
            }}
            QTabBar::tab {{
                background-color: {COLORS['background']};
                color: {COLORS['text_primary']};
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
            QTabBar::tab:selected {{
                background-color: {COLORS['primary']};
                color: white;
            }}
            QTabBar::tab:hover {{
                background-color: {COLORS['hover']};
            }}
        """)
        
        # إضافة التبويبات
        self.add_dashboard_tab()
        self.add_patients_tab()
        self.add_appointments_tab()
        self.add_treatments_tab()
        self.add_invoices_tab()
        
        parent_layout.addWidget(self.tab_widget)
        
    def add_dashboard_tab(self):
        """إضافة تبويب لوحة التحكم"""
        dashboard = QWidget()
        layout = QVBoxLayout(dashboard)
        
        # عنوان الترحيب
        welcome_label = QLabel("مرحباً بك في نظام إدارة العيادة")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 24px;
                font-weight: bold;
                margin: 30px;
            }}
        """)
        layout.addWidget(welcome_label)
        
        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()
        
        # إحصائية المرضى
        patients_count = len(self.db.execute_query("SELECT * FROM patients"))
        self.create_stat_card(stats_layout, "👥", "إجمالي المرضى", str(patients_count), COLORS['primary'])
        
        # إحصائية المواعيد اليوم
        today_appointments = len(self.db.execute_query("SELECT * FROM appointments WHERE appointment_date = date('now')"))
        self.create_stat_card(stats_layout, "📅", "مواعيد اليوم", str(today_appointments), COLORS['secondary'])
        
        # إحصائية العلاجات المعلقة
        pending_treatments = len(self.db.execute_query("SELECT * FROM treatments WHERE status = 'planned'"))
        self.create_stat_card(stats_layout, "🦷", "علاجات معلقة", str(pending_treatments), COLORS['warning'])
        
        # إحصائية الفواتير المعلقة
        pending_invoices = len(self.db.execute_query("SELECT * FROM invoices WHERE status = 'pending'"))
        self.create_stat_card(stats_layout, "💰", "فواتير معلقة", str(pending_invoices), COLORS['error'])
        
        layout.addLayout(stats_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(dashboard, "🏠 لوحة التحكم")
        
    def create_stat_card(self, parent_layout, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 12px;
                padding: 20px;
                margin: 10px;
            }}
            QFrame:hover {{
                border-color: {color};
            }}
        """)
        
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 40px;
                color: {color};
                margin-bottom: 10px;
            }}
        """)
        card_layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                font-weight: bold;
                color: {color};
                margin-bottom: 5px;
            }}
        """)
        card_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                color: {COLORS['text_secondary']};
            }}
        """)
        card_layout.addWidget(title_label)
        
        parent_layout.addWidget(card)

    def add_patients_tab(self):
        """إضافة تبويب إدارة المرضى"""
        patients_widget = QWidget()
        layout = QVBoxLayout(patients_widget)

        # شريط البحث والأزرار
        search_layout = QHBoxLayout()

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("البحث عن مريض...")
        self.search_input.setStyleSheet(f"""
            QLineEdit {{
                border: 2px solid {COLORS['border']};
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: {COLORS['surface']};
            }}
        """)
        search_layout.addWidget(self.search_input)

        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.setStyleSheet(BUTTON_STYLES['outline'])
        search_layout.addWidget(search_btn)

        # زر مريض جديد
        new_patient_btn = QPushButton("➕ مريض جديد")
        new_patient_btn.setStyleSheet(BUTTON_STYLES['primary'])
        new_patient_btn.clicked.connect(self.add_new_patient)
        search_layout.addWidget(new_patient_btn)

        layout.addLayout(search_layout)

        # جدول المرضى
        self.patients_table = QTableWidget()
        self.patients_table.setColumnCount(6)
        self.patients_table.setHorizontalHeaderLabels([
            "الرقم", "الاسم", "العمر", "الجنس", "رقم الهاتف", "تاريخ التسجيل"
        ])

        # تحميل بيانات المرضى
        self.load_patients_data()

        # إضافة أحداث الجدول
        self.patients_table.doubleClicked.connect(self.edit_patient)

        layout.addWidget(self.patients_table)

        self.tab_widget.addTab(patients_widget, "👥 المرضى")

    def add_appointments_tab(self):
        """إضافة تبويب المواعيد"""
        appointments_widget = QWidget()
        layout = QVBoxLayout(appointments_widget)

        # عنوان القسم
        title = QLabel("إدارة المواعيد")
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(title)

        # شريط الأزرار
        buttons_layout = QHBoxLayout()

        new_appointment_btn = QPushButton("📅 موعد جديد")
        new_appointment_btn.setStyleSheet(BUTTON_STYLES['primary'])
        buttons_layout.addWidget(new_appointment_btn)

        view_calendar_btn = QPushButton("📆 عرض التقويم")
        view_calendar_btn.setStyleSheet(BUTTON_STYLES['secondary'])
        buttons_layout.addWidget(view_calendar_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # جدول المواعيد
        self.appointments_table = QTableWidget()
        self.appointments_table.setColumnCount(5)
        self.appointments_table.setHorizontalHeaderLabels([
            "المريض", "التاريخ", "الوقت", "الحالة", "ملاحظات"
        ])

        layout.addWidget(self.appointments_table)

        self.tab_widget.addTab(appointments_widget, "📅 المواعيد")

    def add_treatments_tab(self):
        """إضافة تبويب العلاجات"""
        treatments_widget = QWidget()
        layout = QVBoxLayout(treatments_widget)

        # عنوان القسم
        title = QLabel("إدارة العلاجات")
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(title)

        # شريط الأزرار
        buttons_layout = QHBoxLayout()

        new_treatment_btn = QPushButton("🦷 علاج جديد")
        new_treatment_btn.setStyleSheet(BUTTON_STYLES['primary'])
        buttons_layout.addWidget(new_treatment_btn)

        tooth_chart_btn = QPushButton("🗺️ خريطة الأسنان")
        tooth_chart_btn.setStyleSheet(BUTTON_STYLES['secondary'])
        buttons_layout.addWidget(tooth_chart_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # جدول العلاجات
        self.treatments_table = QTableWidget()
        self.treatments_table.setColumnCount(6)
        self.treatments_table.setHorizontalHeaderLabels([
            "المريض", "رقم السن", "نوع العلاج", "التكلفة", "التاريخ", "الحالة"
        ])

        layout.addWidget(self.treatments_table)

        self.tab_widget.addTab(treatments_widget, "🦷 العلاجات")

    def add_invoices_tab(self):
        """إضافة تبويب الفواتير"""
        invoices_widget = QWidget()
        layout = QVBoxLayout(invoices_widget)

        # عنوان القسم
        title = QLabel("إدارة الفواتير والمدفوعات")
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(title)

        # شريط الأزرار
        buttons_layout = QHBoxLayout()

        new_invoice_btn = QPushButton("💰 فاتورة جديدة")
        new_invoice_btn.setStyleSheet(BUTTON_STYLES['primary'])
        buttons_layout.addWidget(new_invoice_btn)

        payment_btn = QPushButton("💳 تسجيل دفعة")
        payment_btn.setStyleSheet(BUTTON_STYLES['secondary'])
        buttons_layout.addWidget(payment_btn)

        reports_btn = QPushButton("📊 التقارير المالية")
        reports_btn.setStyleSheet(BUTTON_STYLES['outline'])
        buttons_layout.addWidget(reports_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(6)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "المريض", "المبلغ الإجمالي", "المدفوع", "المتبقي", "الحالة"
        ])

        layout.addWidget(self.invoices_table)

        self.tab_widget.addTab(invoices_widget, "💰 الفواتير")

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # معلومات العيادة
        clinic_info = QLabel("عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان")
        status_bar.addWidget(clinic_info)

        status_bar.addPermanentWidget(QLabel("جاهز"))

        # التاريخ والوقت
        self.datetime_label = QLabel()
        self.update_datetime()
        status_bar.addPermanentWidget(self.datetime_label)

        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)

    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        from datetime import datetime
        now = datetime.now()
        self.datetime_label.setText(now.strftime("%Y-%m-%d %H:%M:%S"))

    def load_patients_data(self):
        """تحميل بيانات المرضى"""
        patients = self.db.execute_query("SELECT * FROM patients ORDER BY created_date DESC")

        self.patients_table.setRowCount(len(patients))

        for row, patient in enumerate(patients):
            self.patients_table.setItem(row, 0, QTableWidgetItem(str(patient[0])))  # ID
            self.patients_table.setItem(row, 1, QTableWidgetItem(patient[1]))       # Name
            self.patients_table.setItem(row, 2, QTableWidgetItem(str(patient[2])))  # Age
            self.patients_table.setItem(row, 3, QTableWidgetItem(patient[3]))       # Gender
            self.patients_table.setItem(row, 4, QTableWidgetItem(patient[4]))       # Phone
            self.patients_table.setItem(row, 5, QTableWidgetItem(patient[7]))       # Created Date

        # تعديل عرض الأعمدة
        header = self.patients_table.horizontalHeader()
        header.setStretchLastSection(True)

    # دوال التنقل بين التبويبات
    def show_patients_tab(self):
        self.tab_widget.setCurrentIndex(1)

    def show_appointments_tab(self):
        self.tab_widget.setCurrentIndex(2)

    def show_treatments_tab(self):
        self.tab_widget.setCurrentIndex(3)

    def show_invoices_tab(self):
        self.tab_widget.setCurrentIndex(4)

    def show_reports_tab(self):
        # يمكن إضافة تبويب التقارير لاحقاً
        pass

    def show_settings_tab(self):
        # يمكن إضافة تبويب الإعدادات لاحقاً
        pass

    def backup_database(self):
        """عمل نسخة احتياطية من قاعدة البيانات"""
        QMessageBox.information(self, "نسخ احتياطي", "سيتم تطوير هذه الميزة قريباً")

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "نظام إدارة عيادة الأسنان\n"
                         "عيادة الدكتورة ضياء أبو جلبان\n"
                         "الإصدار 1.0\n\n"
                         "تم التطوير بواسطة فريق التطوير")

    def add_new_patient(self):
        """إضافة مريض جديد"""
        dialog = PatientDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_patients_data()  # إعادة تحميل البيانات

    def edit_patient(self):
        """تعديل بيانات مريض"""
        current_row = self.patients_table.currentRow()
        if current_row >= 0:
            patient_id = int(self.patients_table.item(current_row, 0).text())
            dialog = PatientDialog(patient_id=patient_id, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_patients_data()  # إعادة تحميل البيانات
