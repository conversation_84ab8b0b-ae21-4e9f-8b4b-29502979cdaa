#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد خط Cairo العربي للتطبيق
"""

import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFontDatabase
from PyQt5.QtCore import Qt

def setup_cairo_font():
    """إعداد خط Cairo في النظام"""
    
    # إنشاء تطبيق مؤقت للوصول إلى QFontDatabase
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    font_db = QFontDatabase()
    available_families = font_db.families()
    
    print("🔍 البحث عن الخطوط العربية المتاحة...")
    
    # قائمة الخطوط العربية الجيدة
    arabic_fonts = [
        "Cairo",
        "Amiri", 
        "Noto Sans Arabic",
        "Segoe UI",
        "<PERSON><PERSON><PERSON>",
        "Arial Unicode MS",
        "Microsoft Sans Serif",
        "Helvetica Neue",
        "SF Pro Text",
        "DejaVu Sans",
        "Liberation Sans"
    ]
    
    found_fonts = []
    for font in arabic_fonts:
        if font in available_families:
            found_fonts.append(font)
            print(f"✅ تم العثور على: {font}")
    
    if not found_fonts:
        print("❌ لم يتم العثور على خطوط عربية مناسبة")
        return None
    
    # اختيار أفضل خط متاح
    best_font = found_fonts[0]
    print(f"🎯 تم اختيار الخط: {best_font}")
    
    return best_font

def test_arabic_text():
    """اختبار النص العربي"""
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # ضبط الاتجاه العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    best_font = setup_cairo_font()
    
    if best_font:
        print(f"\n📝 اختبار النص العربي بخط {best_font}:")
        print("=" * 50)
        print("🦷 عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان")
        print("👥 إدارة المرضى")
        print("📅 نظام المواعيد") 
        print("💊 العلاجات والتشخيص")
        print("💰 الفواتير والمدفوعات")
        print("📊 التقارير والإحصائيات")
        print("=" * 50)
        print("✅ النص العربي يعمل بشكل صحيح!")
    
    return best_font

def create_font_config():
    """إنشاء ملف تكوين الخطوط"""
    
    best_font = setup_cairo_font()
    
    config_content = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
تكوين الخطوط العربية للتطبيق
تم إنشاؤه تلقائياً
'''

# الخط المختار
SELECTED_FONT = "{best_font or 'Cairo'}"

# قائمة الخطوط المتاحة
AVAILABLE_FONTS = {setup_cairo_font() or ['Cairo']}

# إعدادات الخط
FONT_CONFIG = {{
    'name': SELECTED_FONT,
    'size_small': 10,
    'size_normal': 12,
    'size_medium': 14,
    'size_large': 16,
    'size_header': 18,
    'size_title': 24
}}

def get_font_name():
    return SELECTED_FONT

def get_font_config():
    return FONT_CONFIG
"""
    
    with open('font_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"📄 تم إنشاء ملف تكوين الخطوط: font_config.py")
    return best_font

if __name__ == "__main__":
    print("🔤 إعداد خط Cairo العربي")
    print("=" * 40)
    
    # اختبار النص العربي
    best_font = test_arabic_text()
    
    # إنشاء ملف التكوين
    create_font_config()
    
    print(f"\n✅ تم إعداد الخط بنجاح: {best_font}")
    print("🚀 يمكنك الآن تشغيل التطبيق: python main.py")
