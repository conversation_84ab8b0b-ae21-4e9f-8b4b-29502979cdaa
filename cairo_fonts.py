#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة خط Cairo العربي الموثوق مع ضبط الاتجاه والترميز
"""

from PyQt5.QtGui import QFontDatabase, QFont
from PyQt5.QtCore import Qt, QDir
from PyQt5.QtWidgets import QApplication
import os
import platform
import urllib.request
import zipfile

class CairoFontManager:
    """مدير خط Cairo العربي الموثوق"""

    def __init__(self):
        self.system = platform.system()
        self.font_db = None
        self.best_font = "Cairo"
        self._initialized = False
        self.cairo_installed = False
        
        # مجلد الخطوط المحلي
        self.fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        if not os.path.exists(self.fonts_dir):
            os.makedirs(self.fonts_dir)
        
        # رابط تحميل خط Cairo من Google Fonts
        self.cairo_download_url = "https://fonts.google.com/download?family=Cairo"

    def _ensure_initialized(self):
        """التأكد من تهيئة مدير الخطوط"""
        if not self._initialized:
            try:
                self.font_db = QFontDatabase()
                self._load_cairo_font()
                self.best_font = self.detect_best_arabic_font()
                self._initialized = True
            except Exception as e:
                print(f"خطأ في تهيئة الخطوط: {e}")
                self.best_font = "Cairo"
                self._initialized = False

    def _load_cairo_font(self):
        """تحميل وتثبيت خط Cairo"""
        if not self.font_db:
            return
            
        available_families = self.font_db.families()
        
        # التحقق من وجود خط Cairo مسبقاً
        if "Cairo" in available_families:
            self.cairo_installed = True
            print("✅ خط Cairo متاح بالفعل في النظام")
            return
            
        # محاولة تحميل خط Cairo من الملفات المحلية
        cairo_files = [
            "Cairo-Regular.ttf",
            "Cairo-Bold.ttf", 
            "Cairo-SemiBold.ttf",
            "Cairo-Light.ttf"
        ]
        
        fonts_loaded = 0
        for font_file in cairo_files:
            font_path = os.path.join(self.fonts_dir, font_file)
            if os.path.exists(font_path):
                font_id = self.font_db.addApplicationFont(font_path)
                if font_id != -1:
                    fonts_loaded += 1
                    print(f"✅ تم تحميل {font_file}")
        
        if fonts_loaded > 0:
            self.cairo_installed = True
            print(f"✅ تم تحميل {fonts_loaded} ملفات خط Cairo")
        else:
            print("⚠️ لم يتم العثور على ملفات خط Cairo محلياً")
            self._download_cairo_font()

    def _download_cairo_font(self):
        """تحميل خط Cairo من الإنترنت (اختياري)"""
        try:
            print("🔄 محاولة تحميل خط Cairo...")
            # هذه وظيفة اختيارية - يمكن تطويرها لاحقاً
            # في الوقت الحالي، سنعتمد على الخطوط المتاحة في النظام
            pass
        except Exception as e:
            print(f"❌ فشل في تحميل خط Cairo: {e}")

    def detect_best_arabic_font(self):
        """اكتشاف أفضل خط عربي متاح مع أولوية لـ Cairo"""
        if not self.font_db:
            return "Cairo"

        available_families = self.font_db.families()
        
        # قائمة الخطوط المفضلة مع أولوية للخطوط العربية الموثوقة
        if self.system == "Windows":
            preferred_fonts = [
                "Cairo",
                "Segoe UI",  # خط موثوق لـ Windows
                "Tahoma",
                "Arial Unicode MS",
                "Microsoft Sans Serif",
                "Arial"
            ]
        elif self.system == "Darwin":  # macOS
            preferred_fonts = [
                "Cairo",
                "SF Pro Text",
                "Helvetica Neue",
                "Arial Unicode MS",
                "Arial"
            ]
        else:  # Linux
            preferred_fonts = [
                "Cairo",
                "Noto Sans Arabic",
                "DejaVu Sans",
                "Liberation Sans",
                "Arial"
            ]
        
        # إزالة القيم الفارغة
        preferred_fonts = [f for f in preferred_fonts if f is not None]

        # البحث عن أول خط متاح
        for font_name in preferred_fonts:
            if font_name in available_families:
                print(f"✅ تم اختيار الخط: {font_name}")
                return font_name
        
        print("⚠️ استخدام الخط الافتراضي: Cairo")
        return "Cairo"

    def get_font(self, size=12, weight=QFont.Normal):
        """الحصول على خط مع الحجم والوزن المحددين"""
        self._ensure_initialized()
        font = QFont(self.best_font)
        font.setPointSize(size)
        font.setWeight(weight)
        
        # تحسينات خاصة بالنص العربي
        font.setStyleHint(QFont.SansSerif)
        font.setHintingPreference(QFont.PreferFullHinting)
        
        return font

    def get_header_font(self, size=18):
        """خط للعناوين"""
        return self.get_font(size, QFont.Bold)

    def get_body_font(self, size=12):
        """خط للنصوص العادية"""
        return self.get_font(size, QFont.Normal)

    def get_button_font(self, size=11):
        """خط للأزرار"""
        return self.get_font(size, QFont.DemiBold)

    def setup_rtl_layout(self, widget):
        """ضبط الاتجاه من اليمين لليسار"""
        widget.setLayoutDirection(Qt.RightToLeft)
        
        # ضبط محاذاة النص للعربية
        if hasattr(widget, 'setAlignment'):
            widget.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

    def setup_application_rtl(self, app):
        """ضبط التطبيق كاملاً للاتجاه العربي مع إصلاح مشاكل النص"""

        # ضبط الترميز أولاً
        import sys
        import locale
        import os

        # ضبط متغيرات البيئة للعربية
        os.environ['LANG'] = 'ar_SA.UTF-8'
        os.environ['LC_ALL'] = 'ar_SA.UTF-8'

        # ضبط ترميز Python
        if hasattr(sys, 'setdefaultencoding'):
            sys.setdefaultencoding('utf-8')

        # ضبط locale للعربية
        try:
            locale.setlocale(locale.LC_ALL, 'ar_SA.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'Arabic_Saudi Arabia.1256')
            except:
                try:
                    locale.setlocale(locale.LC_ALL, 'ar')
                except:
                    pass

        # ضبط اتجاه التطبيق
        app.setLayoutDirection(Qt.RightToLeft)

        # إعداد الخط مع تحسينات للعربية
        font = self.get_body_font(12)
        font.setStyleHint(QFont.System)
        font.setStyleStrategy(QFont.PreferAntialias | QFont.PreferQuality)
        font.setHintingPreference(QFont.PreferDefaultHinting)
        app.setFont(font)

        # تحسين عرض النص
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)

        print(f"✅ تم ضبط التطبيق للعربية بخط: {font.family()}")
        print(f"📝 ترميز النظام: {locale.getlocale()}")

        # اختبار النص العربي
        test_text = "مرحباً بكم في عيادة الأسنان"
        print(f"🧪 اختبار النص: {test_text}")

def get_font_info():
    """الحصول على معلومات الخط المستخدم"""
    manager = CairoFontManager()
    manager._ensure_initialized()
    
    return {
        'name': manager.best_font,
        'system': manager.system,
        'cairo_installed': manager.cairo_installed,
        'supports_arabic': True,
        'encoding': 'UTF-8'
    }

def get_arabic_font(size=12, weight=QFont.Normal):
    """دالة مساعدة للحصول على خط عربي"""
    return font_manager.get_font(size, weight)

def setup_widget_rtl(widget):
    """دالة مساعدة لضبط اتجاه الويدجت"""
    font_manager.setup_rtl_layout(widget)

# إنشاء مثيل عام من مدير الخطوط
font_manager = CairoFontManager()

# دالة للحصول على اسم الخط الأفضل
def get_best_font_name():
    """الحصول على اسم أفضل خط متاح"""
    font_manager._ensure_initialized()
    return font_manager.best_font
