#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف الأنماط والتصميم للتطبيق مع دعم خط Cairo العربي
يحتوي على الألوان والخطوط والتأثيرات العصرية
"""

# تحديد الخط العربي الموثوق
ARABIC_FONT = "Segoe UI"  # خط Segoe UI كافتراضي موثوق وواضح

# أحجام الخط المحسنة للوضوح
FONT_SIZES = {
    'title': 18,      # العناوين الرئيسية
    'subtitle': 16,   # العناوين الفرعية
    'body': 14,       # النص العادي
    'button': 13,     # أزرار
    'label': 12,      # تسميات
    'small': 11       # نص صغير
}

def get_best_arabic_font():
    """الحصول على أفضل خط عربي متاح"""
    try:
        from cairo_fonts import get_best_font_name
        return get_best_font_name()
    except:
        return "Segoe UI"  # خط موثوق كبديل

# إعدادات الخطوط فائقة الوضوح
FONTS = {
    'primary': f'{ARABIC_FONT}',
    'size_small': 12,      # حجم صغير واضح
    'size_normal': 16,     # حجم عادي محسن للوضوح
    'size_medium': 18,     # حجم متوسط واضح
    'size_large': 20,      # حجم كبير
    'size_xlarge': 22,     # حجم كبير جداً
    'size_title': 24,      # عناوين فائقة الوضوح
    'size_header': 28      # رؤوس كبيرة وواضحة
}

def get_crystal_clear_font_style(font_name, size):
    """إنشاء نمط خط فائق الوضوح"""
    return f"""
        font-family: "{font_name}", "Segoe UI", "Tahoma", "Arial";
        font-size: {size}px;
        font-weight: normal;
        text-rendering: optimizeLegibility;
    """

# الألوان الأساسية للتطبيق
COLORS = {
    'primary': '#2563eb',        # أزرق عصري
    'primary_dark': '#1d4ed8',   # أزرق داكن
    'secondary': '#10b981',      # أخضر نعناعي
    'accent': '#f59e0b',         # برتقالي ذهبي
    'background': '#f8fafc',     # رمادي فاتح جداً
    'surface': '#ffffff',        # أبيض
    'text_primary': '#1f2937',   # رمادي داكن
    'text_secondary': '#6b7280', # رمادي متوسط
    'success': '#10b981',        # أخضر نجاح
    'warning': '#f59e0b',        # برتقالي تحذير
    'error': '#ef4444',          # أحمر خطأ
    'border': '#e5e7eb',         # رمادي حدود
    'hover': '#f3f4f6',          # رمادي عند التمرير
}

# أنماط الأزرار
BUTTON_STYLES = {
    'primary': f"""
        QPushButton {{
            background-color: {COLORS['primary']};
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 10px;
            {get_crystal_clear_font_style(ARABIC_FONT, FONTS['size_medium'])}
            font-weight: 500;
            min-width: 140px;
            min-height: 20px;
            letter-spacing: 0.5px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary_dark']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary_dark']};
        }}
    """,
    
    'secondary': f"""
        QPushButton {{
            background-color: {COLORS['secondary']};
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-family: '{ARABIC_FONT}';
            font-size: {FONTS['size_medium']}px;
            font-weight: bold;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: #059669;
        }}
        QPushButton:pressed {{
            background-color: #059669;
        }}
    """,
    
    'outline': f"""
        QPushButton {{
            background-color: transparent;
            color: {COLORS['primary']};
            border: 2px solid {COLORS['primary']};
            padding: 10px 22px;
            border-radius: 8px;
            font-family: '{ARABIC_FONT}';
            font-size: {FONTS['size_medium']}px;
            font-weight: bold;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary']};
            color: white;
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary_dark']};
            color: white;
        }}
    """,
    
    'danger': f"""
        QPushButton {{
            background-color: {COLORS['error']};
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-family: '{ARABIC_FONT}';
            font-size: {FONTS['size_medium']}px;
            font-weight: bold;
            min-width: 120px;
        }}
        QPushButton:hover {{
            background-color: #dc2626;
        }}
        QPushButton:pressed {{
            background-color: #dc2626;
        }}
    """
}

# أنماط حقول الإدخال محسنة للوضوح
INPUT_STYLES = f"""
    QLineEdit, QTextEdit, QComboBox, QSpinBox {{
        border: 2px solid {COLORS['border']};
        border-radius: 10px;
        padding: 14px 18px;
        {get_crystal_clear_font_style(ARABIC_FONT, FONTS['size_medium'])}
        background-color: {COLORS['surface']};
        color: {COLORS['text_primary']};
        selection-background-color: {COLORS['primary']};
        selection-color: white;
        min-height: 20px;
        letter-spacing: 0.3px;
        line-height: 1.4;
    }}
    QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {{
        border-color: {COLORS['primary']};
        outline: none;
    }}
    QLineEdit:hover, QTextEdit:hover, QComboBox:hover, QSpinBox:hover {{
        border-color: {COLORS['primary']};
    }}
    QComboBox::drop-down {{
        border: none;
        width: 30px;
    }}
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {COLORS['text_secondary']};
        margin-right: 10px;
    }}
    QSpinBox::up-button, QSpinBox::down-button {{
        width: 20px;
        border: none;
        background-color: {COLORS['primary']};
        color: white;
    }}
    QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
        background-color: {COLORS['primary_dark']};
    }}
"""

# أنماط الجداول
TABLE_STYLES = f"""
    QTableWidget {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: 8px;
        gridline-color: {COLORS['border']};
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
        alternate-background-color: {COLORS['background']};
    }}
    QTableWidget::item {{
        padding: 12px;
        border-bottom: 1px solid {COLORS['border']};
        color: {COLORS['text_primary']};
    }}
    QTableWidget::item:selected {{
        background-color: {COLORS['primary']};
        color: white;
    }}
    QTableWidget::item:hover {{
        background-color: {COLORS['hover']};
    }}
    QHeaderView::section {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        padding: 12px;
        border: none;
        border-bottom: 2px solid {COLORS['primary']};
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
        font-weight: bold;
    }}
"""

# أنماط البطاقات المحسنة
CARD_STYLES = f"""
    QFrame {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: 12px;
        padding: 20px;
        font-family: '{ARABIC_FONT}';
    }}
    QFrame:hover {{
        border-color: {COLORS['primary']};
        background-color: {COLORS['background']};
    }}
    .stats-card {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['surface']},
                                   stop:1 {COLORS['background']});
        border: 2px solid {COLORS['border']};
        border-radius: 15px;
        padding: 25px;
        min-height: 120px;
    }}
    .stats-card:hover {{
        border-color: {COLORS['primary']};
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary']},
                                   stop:1 {COLORS['primary_dark']});
        color: white;
    }}
"""

# أنماط التسميات محسنة للوضوح
LABEL_STYLES = f"""
    QLabel {{
        color: {COLORS['text_primary']};
        {get_crystal_clear_font_style(ARABIC_FONT, FONTS['size_medium'])}
        padding: 8px 12px;
        line-height: 1.4;
        letter-spacing: 0.3px;
    }}
    .title {{
        font-size: {FONTS['size_header']}px;
        font-weight: bold;
        color: {COLORS['primary']};
        margin-bottom: 10px;
    }}
    .subtitle {{
        font-size: {FONTS['size_xlarge']}px;
        font-weight: 600;
        color: {COLORS['text_primary']};
        margin-bottom: 8px;
    }}
    .caption {{
        font-size: {FONTS['size_small']}px;
        color: {COLORS['text_secondary']};
    }}
"""

# النمط الرئيسي للتطبيق
MAIN_STYLE = f"""
    QMainWindow {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_normal']}px;
    }}

    QWidget {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
        font-family: '{ARABIC_FONT}';
    }}

    QMenuBar {{
        background-color: {COLORS['surface']};
        border-bottom: 1px solid {COLORS['border']};
        padding: 4px;
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
    }}

    QMenuBar::item {{
        background-color: transparent;
        padding: 8px 16px;
        border-radius: 4px;
        color: {COLORS['text_primary']};
    }}
    
    QMenuBar::item:selected {{
        background-color: {COLORS['hover']};
    }}
    
    QStatusBar {{
        background-color: {COLORS['surface']};
        border-top: 1px solid {COLORS['border']};
        color: {COLORS['text_secondary']};
    }}
    
    QScrollBar:vertical {{
        background-color: {COLORS['background']};
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['border']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['text_secondary']};
    }}
"""

# نمط تسجيل الدخول محسن للوضوح
LOGIN_STYLE = f"""
    QDialog {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                   stop:0 {COLORS['primary']},
                                   stop:1 {COLORS['secondary']});
        {get_crystal_clear_font_style(ARABIC_FONT, FONTS['size_normal'])}
    }}

    .login-card {{
        background-color: {COLORS['surface']};
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }}
    
    .logo-text {{
        color: {COLORS['primary']};
        font-size: 28px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
    }}
    
    .clinic-subtitle {{
        color: {COLORS['text_secondary']};
        font-size: 16px;
        text-align: center;
        margin-bottom: 30px;
    }}
"""

# أنماط الشريط الجانبي المحسنة
SIDEBAR_STYLES = f"""
    QListWidget {{
        background-color: {COLORS['surface']};
        border: none;
        border-radius: 10px;
        padding: 10px;
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
        outline: none;
    }}

    QListWidget::item {{
        background-color: transparent;
        border: none;
        border-radius: 8px;
        padding: 15px 20px;
        margin: 2px 0px;
        color: {COLORS['text_primary']};
        font-weight: 500;
    }}

    QListWidget::item:selected {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                   stop:0 {COLORS['primary']},
                                   stop:1 {COLORS['primary_dark']});
        color: white;
        font-weight: bold;
    }}

    QListWidget::item:hover {{
        background-color: {COLORS['hover']};
        color: {COLORS['primary']};
    }}

    QListWidget::item:selected:hover {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                   stop:0 {COLORS['primary_dark']},
                                   stop:1 {COLORS['primary']});
        color: white;
    }}
"""

# أنماط التبويبات المحسنة
TAB_STYLES = f"""
    QTabWidget::pane {{
        border: 1px solid {COLORS['border']};
        border-radius: 10px;
        background-color: {COLORS['surface']};
        padding: 10px;
    }}

    QTabBar::tab {{
        background-color: {COLORS['background']};
        border: 1px solid {COLORS['border']};
        border-bottom: none;
        border-radius: 8px 8px 0px 0px;
        padding: 12px 20px;
        margin-right: 2px;
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
        font-weight: 500;
        color: {COLORS['text_secondary']};
        min-width: 120px;
    }}

    QTabBar::tab:selected {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary']},
                                   stop:1 {COLORS['primary_dark']});
        color: white;
        font-weight: bold;
        border-color: {COLORS['primary']};
    }}

    QTabBar::tab:hover {{
        background-color: {COLORS['hover']};
        color: {COLORS['primary']};
    }}

    QTabBar::tab:selected:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary_dark']},
                                   stop:1 {COLORS['primary']});
    }}
"""

# أنماط شريط الأدوات المحسن
TOOLBAR_STYLES = f"""
    QToolBar {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['surface']},
                                   stop:1 {COLORS['background']});
        border: none;
        border-bottom: 2px solid {COLORS['border']};
        padding: 8px;
        spacing: 5px;
        font-family: '{ARABIC_FONT}';
    }}

    QToolButton {{
        background-color: transparent;
        border: 1px solid transparent;
        border-radius: 8px;
        padding: 10px 15px;
        margin: 2px;
        font-family: '{ARABIC_FONT}';
        font-size: {FONTS['size_medium']}px;
        color: {COLORS['text_primary']};
    }}

    QToolButton:hover {{
        background-color: {COLORS['hover']};
        border-color: {COLORS['primary']};
        color: {COLORS['primary']};
    }}

    QToolButton:pressed {{
        background-color: {COLORS['primary']};
        color: white;
    }}
"""

def get_tooth_color(condition):
    """إرجاع لون السن حسب حالته"""
    colors = {
        'healthy': '#10b981',      # أخضر صحي
        'cavity': '#ef4444',       # أحمر تسوس
        'filled': '#3b82f6',       # أزرق حشوة
        'crown': '#f59e0b',        # ذهبي تاج
        'missing': '#6b7280',      # رمادي مفقود
        'root_canal': '#8b5cf6',   # بنفسجي علاج عصب
        'extraction': '#000000',   # أسود خلع
    }
    return colors.get(condition, '#e5e7eb')  # رمادي افتراضي
