# دليل الخطوط العربية - نظام إدارة عيادة الأسنان

## 🔤 نظام الخطوط الموثوق

### الخط الأساسي المستخدم
- **Segoe UI**: الخط الأساسي الموثوق لنظام Windows
- **دعم كامل للعربية**: عرض واضح ومقروء للنص العربي
- **ترميز UTF-8**: ضمان عرض جميع الأحرف العربية بشكل صحيح

---

## 🎯 الخطوط المدعومة حسب نظام التشغيل

### Windows
1. **Segoe UI** (الأفضل) ✅
2. **Tahoma** 
3. **Arial Unicode MS**
4. **Microsoft Sans Serif**
5. **Arial**

### macOS
1. **SF Pro Text** (الأفضل)
2. **Helvetica Neue**
3. **Arial Unicode MS**
4. **Arial**

### Linux
1. **Noto Sans Arabic** (الأفضل)
2. **DejaVu Sans**
3. **Liberation Sans**
4. **Arial**

---

## ⚙️ إعدادات الخطوط

### أحجام الخطوط
- **صغير**: 10px - للملاحظات والتفاصيل
- **عادي**: 12px - للنصوص العامة
- **متوسط**: 14px - للعناوين الفرعية
- **كبير**: 16px - للعناوين
- **رئيسي**: 18px - للعناوين الرئيسية
- **عنوان**: 24px - لعنوان التطبيق

### أوزان الخطوط
- **عادي** (Normal): للنصوص العامة
- **متوسط** (DemiBold): للأزرار
- **عريض** (Bold): للعناوين

---

## 🔧 اختبار الخطوط

### تشغيل أداة اختبار الخطوط
```bash
python font_test.py
```

### تشغيل إعداد الخطوط
```bash
python setup_cairo.py
```

### التحقق من الخط المستخدم
```bash
python -c "from cairo_fonts import get_font_info; print(get_font_info())"
```

---

## 🌐 الاتجاه العربي (RTL)

### الإعدادات المطبقة
- **اتجاه التطبيق**: من اليمين لليسار
- **محاذاة النص**: يمين ووسط
- **ترتيب العناصر**: عكسي للواجهة العربية

### النوافذ المدعومة
- ✅ نافذة تسجيل الدخول
- ✅ النافذة الرئيسية
- ✅ نوافذ إدارة المرضى
- ✅ مخطط الأسنان
- ✅ جميع النوافذ الفرعية

---

## 📝 نصائح لتحسين جودة النص

### 1. تحسين العرض
- تأكد من دقة الشاشة 100% أو أعلى
- استخدم إعدادات ClearType في Windows
- تجنب التكبير الزائد للنص

### 2. إعدادات النظام
- تأكد من تثبيت حزمة اللغة العربية
- ضبط المنطقة الزمنية للمملكة العربية السعودية
- تفعيل دعم اللغات من اليمين لليسار

### 3. استكشاف الأخطاء
- إذا ظهر النص مقطوعاً: أعد تشغيل التطبيق
- إذا كان الاتجاه خاطئاً: تحقق من إعدادات النظام
- إذا كانت الخطوط غير واضحة: جرب خط آخر

---

## 🔄 تحديث الخطوط

### إضافة خط Cairo (اختياري)
1. تحميل خط Cairo من [Google Fonts](https://fonts.google.com/specimen/Cairo)
2. نسخ ملفات .ttf إلى مجلد `fonts/`
3. إعادة تشغيل التطبيق

### ملفات Cairo المطلوبة
- `Cairo-Regular.ttf`
- `Cairo-Bold.ttf`
- `Cairo-SemiBold.ttf`
- `Cairo-Light.ttf`

---

## 🐛 حل مشاكل الخطوط

### مشاكل شائعة

#### النص غير مقروء
```bash
# تشغيل اختبار الخطوط
python font_test.py

# إعادة إعداد الخطوط
python setup_cairo.py
```

#### الاتجاه خاطئ
- تأكد من إعدادات اللغة في النظام
- أعد تشغيل التطبيق
- تحقق من دعم RTL في النظام

#### خطوط مفقودة
- التطبيق سيعمل بالخط الافتراضي
- يمكن تثبيت خطوط إضافية من إعدادات النظام
- استخدم أداة `setup_cairo.py` لاختيار أفضل خط متاح

---

## 📊 معلومات تقنية

### الترميز المستخدم
- **UTF-8**: لجميع النصوص العربية
- **Unicode**: دعم كامل للأحرف العربية
- **RTL**: اتجاه من اليمين لليسار

### مكتبات الخطوط
- **QFontDatabase**: إدارة الخطوط في PyQt5
- **QFont**: تطبيق الخطوط على العناصر
- **QApplication**: ضبط الخط العام للتطبيق

### ملفات الخطوط
- `cairo_fonts.py`: مدير الخطوط الرئيسي
- `font_config.py`: تكوين الخطوط (يتم إنشاؤه تلقائياً)
- `styles.py`: أنماط الخطوط والألوان

---

## ✅ التحقق من الإعداد

### قائمة فحص سريعة
- [ ] النص العربي يظهر بوضوح
- [ ] الاتجاه من اليمين لليسار
- [ ] الأزرار والقوائم في المكان الصحيح
- [ ] لا توجد أحرف مقطوعة أو مشوهة
- [ ] العناوين والنصوص متناسقة

### في حالة وجود مشاكل
1. تشغيل `python setup_cairo.py`
2. إعادة تشغيل التطبيق
3. التحقق من إعدادات النظام
4. مراجعة هذا الدليل

---

**تم إعداد هذا الدليل لضمان أفضل تجربة للخطوط العربية في النظام** 🎯
