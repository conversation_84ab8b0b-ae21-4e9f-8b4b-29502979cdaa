#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الخطوط العربية للتطبيق - خط Cairo الموثوق
"""

from PyQt5.QtGui import QFontDatabase, QFont
from PyQt5.QtCore import QStandardPaths, QDir
import os
import platform
import urllib.request
import zipfile

class ArabicFontManager:
    """مدير الخطوط العربية - خط Cairo الموثوق"""

    def __init__(self):
        self.system = platform.system()
        self.font_db = None
        self.best_font = "Cairo"  # خط Cairo كافتراضي
        self._initialized = False
        self.cairo_installed = False

        # مجلد الخطوط المحلي
        self.fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        if not os.path.exists(self.fonts_dir):
            os.makedirs(self.fonts_dir)

    def _ensure_initialized(self):
        """التأكد من تهيئة مدير الخطوط"""
        if not self._initialized:
            try:
                self.font_db = QFontDatabase()
                self._load_cairo_font()
                self.best_font = self.detect_best_arabic_font()
                self._initialized = True
            except:
                # في حالة عدم توفر QGuiApplication بعد
                self.best_font = "Cairo"
                self._initialized = False
        
    def detect_best_arabic_font(self):
        """اكتشاف أفضل خط عربي متاح"""

        if not self.font_db:
            return "Arial"

        # خطوط Windows المفضلة
        windows_fonts = [
            "Segoe UI",
            "Tahoma",
            "Arial Unicode MS",
            "Microsoft Sans Serif",
            "Calibri",
            "Verdana"
        ]

        # خطوط macOS المفضلة
        macos_fonts = [
            "SF Pro Text",
            "Helvetica Neue",
            "Arial Unicode MS",
            "Lucida Grande"
        ]

        # خطوط Linux المفضلة
        linux_fonts = [
            "Noto Sans Arabic",
            "DejaVu Sans",
            "Liberation Sans",
            "Ubuntu",
            "Droid Sans"
        ]

        # اختيار قائمة الخطوط حسب النظام
        if self.system == "Windows":
            preferred_fonts = windows_fonts
        elif self.system == "Darwin":  # macOS
            preferred_fonts = macos_fonts
        else:  # Linux وأنظمة أخرى
            preferred_fonts = linux_fonts

        try:
            # البحث عن أفضل خط متاح
            available_families = self.font_db.families()

            for font_name in preferred_fonts:
                if font_name in available_families:
                    # التحقق من دعم الخط للعربية
                    if self.supports_arabic(font_name):
                        return font_name

            # إذا لم يتم العثور على خط مناسب، البحث في جميع الخطوط المتاحة
            for family in available_families:
                if self.supports_arabic(family):
                    return family

            # الخط الافتراضي كحل أخير
            return available_families[0] if available_families else "Arial"
        except:
            return "Arial"
    
    def supports_arabic(self, font_family):
        """التحقق من دعم الخط للغة العربية"""
        try:
            font = QFont(font_family)
            # اختبار بعض الأحرف العربية
            arabic_chars = "أبجدهوز"
            
            # في PyQt5، يمكننا استخدام QFontMetrics للتحقق
            from PyQt5.QtGui import QFontMetrics
            metrics = QFontMetrics(font)
            
            # إذا كان الخط يدعم الأحرف العربية، فسيعطي عرض صحيح
            for char in arabic_chars:
                if metrics.width(char) <= 0:
                    return False
            return True
        except:
            return True  # افتراض الدعم في حالة الخطأ
    
    def get_font(self, size=12, weight=QFont.Normal, italic=False):
        """الحصول على خط مُعد مسبقاً"""
        self._ensure_initialized()

        font = QFont(self.best_font or "Arial")
        font.setPointSize(size)
        font.setWeight(weight)
        font.setItalic(italic)

        # تحسينات إضافية للنص العربي
        font.setStyleHint(QFont.System)
        font.setStyleStrategy(QFont.PreferAntialias)

        return font
    
    def get_title_font(self, size=24):
        """خط العناوين"""
        return self.get_font(size, QFont.Bold)
    
    def get_subtitle_font(self, size=18):
        """خط العناوين الفرعية"""
        return self.get_font(size, QFont.DemiBold)
    
    def get_body_font(self, size=14):
        """خط النص العادي"""
        return self.get_font(size, QFont.Normal)
    
    def get_caption_font(self, size=12):
        """خط النصوص الصغيرة"""
        return self.get_font(size, QFont.Normal)
    
    def get_button_font(self, size=14):
        """خط الأزرار"""
        return self.get_font(size, QFont.DemiBold)

# إنشاء مثيل عام لمدير الخطوط
font_manager = ArabicFontManager()

# دوال مساعدة للوصول السريع
def get_arabic_font():
    """الحصول على اسم أفضل خط عربي"""
    font_manager._ensure_initialized()
    return font_manager.best_font or "Arial"

def get_title_font(size=24):
    """الحصول على خط العناوين"""
    return font_manager.get_title_font(size)

def get_body_font(size=14):
    """الحصول على خط النص العادي"""
    return font_manager.get_body_font(size)

def get_button_font(size=14):
    """الحصول على خط الأزرار"""
    return font_manager.get_button_font(size)

def apply_arabic_font_to_widget(widget, size=14, weight=QFont.Normal):
    """تطبيق الخط العربي على عنصر واجهة"""
    font = font_manager.get_font(size, weight)
    widget.setFont(font)

# معلومات الخط المكتشف
def get_font_info():
    """الحصول على معلومات الخط المستخدم"""
    return {
        'name': font_manager.best_font,
        'system': font_manager.system,
        'supports_arabic': font_manager.supports_arabic(font_manager.best_font)
    }

# طباعة معلومات الخط للتشخيص
if __name__ == "__main__":
    info = get_font_info()
    print(f"أفضل خط عربي مكتشف: {info['name']}")
    print(f"نظام التشغيل: {info['system']}")
    print(f"يدعم العربية: {info['supports_arabic']}")
