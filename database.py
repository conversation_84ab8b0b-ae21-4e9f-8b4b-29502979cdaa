import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="clinic_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                age INTEGER,
                gender TEXT,
                phone TEXT,
                medical_notes TEXT,
                image_path TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواعيد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS appointments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                appointment_date DATE,
                appointment_time TIME,
                status TEXT DEFAULT 'scheduled',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول العلاجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                tooth_number TEXT,
                treatment_type TEXT,
                description TEXT,
                cost REAL,
                treatment_date DATE,
                status TEXT DEFAULT 'planned',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول خريطة الأسنان
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tooth_chart (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                tooth_number TEXT,
                tooth_type TEXT,
                condition_status TEXT,
                notes TEXT,
                color_code TEXT,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                total_amount REAL,
                paid_amount REAL DEFAULT 0,
                remaining_amount REAL,
                invoice_date DATE,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول المدفوعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                patient_id INTEGER,
                amount REAL,
                payment_date DATE,
                payment_method TEXT,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول صور الأشعة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medical_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                image_path TEXT,
                image_type TEXT,
                description TEXT,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE,
                setting_value TEXT,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إدراج الإعدادات الافتراضية
        cursor.execute('''
            INSERT OR IGNORE INTO settings (setting_key, setting_value) 
            VALUES ('clinic_name', 'عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO settings (setting_key, setting_value) 
            VALUES ('doctor_name', 'د. ضياء أبو جلبان')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO settings (setting_key, setting_value) 
            VALUES ('work_hours_start', '09:00')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO settings (setting_key, setting_value) 
            VALUES ('work_hours_end', '21:00')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO settings (setting_key, setting_value) 
            VALUES ('appointment_duration', '30')
        ''')
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        result = cursor.fetchall()
        conn.commit()
        conn.close()
        return result
    
    def insert_data(self, table, data):
        """إدراج بيانات جديدة"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, list(data.values()))
        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return last_id
    
    def update_data(self, table, data, condition):
        """تحديث البيانات"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {condition}"
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, list(data.values()))
        conn.commit()
        conn.close()
    
    def delete_data(self, table, condition, params=None):
        """حذف البيانات"""
        query = f"DELETE FROM {table} WHERE {condition}"
        
        conn = self.get_connection()
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        conn.commit()
        conn.close()
