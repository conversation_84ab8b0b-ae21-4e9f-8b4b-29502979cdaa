#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إصلاح مشاكل اللغة العربية في التطبيق
"""

import sys
import os
import locale
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QFontDatabase

def diagnose_arabic_support():
    """تشخيص دعم اللغة العربية في النظام"""
    
    print("🔍 تشخيص دعم اللغة العربية")
    print("=" * 50)
    
    # 1. فحص ترميز Python
    print(f"🐍 ترميز Python الافتراضي: {sys.getdefaultencoding()}")
    print(f"📁 ترميز نظام الملفات: {sys.getfilesystemencoding()}")
    
    # 2. فحص locale
    try:
        current_locale = locale.getlocale()
        print(f"🌍 Locale الحالي: {current_locale}")
    except:
        print("❌ خطأ في قراءة locale")
    
    # 3. فحص متغيرات البيئة
    lang_vars = ['LANG', 'LC_ALL', 'LC_CTYPE']
    for var in lang_vars:
        value = os.environ.get(var, 'غير محدد')
        print(f"🔧 {var}: {value}")
    
    # 4. اختبار النص العربي
    arabic_text = "مرحباً بكم في عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان"
    print(f"📝 اختبار النص العربي:")
    print(f"   النص: {arabic_text}")
    print(f"   الطول: {len(arabic_text)} حرف")
    print(f"   UTF-8: {arabic_text.encode('utf-8')[:50]}...")
    
    return True

def fix_arabic_encoding():
    """إصلاح مشاكل ترميز اللغة العربية"""
    
    print("\n🔧 إصلاح ترميز اللغة العربية")
    print("=" * 50)
    
    # 1. ضبط متغيرات البيئة
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['LANG'] = 'ar_SA.UTF-8'
    os.environ['LC_ALL'] = 'ar_SA.UTF-8'
    os.environ['LC_CTYPE'] = 'ar_SA.UTF-8'
    print("✅ تم ضبط متغيرات البيئة")
    
    # 2. ضبط locale
    locales_to_try = [
        'ar_SA.UTF-8',
        'Arabic_Saudi Arabia.1256',
        'ar_SA',
        'ar',
        'en_US.UTF-8',
        'C.UTF-8'
    ]
    
    for loc in locales_to_try:
        try:
            locale.setlocale(locale.LC_ALL, loc)
            print(f"✅ تم ضبط locale إلى: {loc}")
            break
        except:
            continue
    else:
        print("⚠️ لم يتم العثور على locale مناسب")
    
    # 3. ضبط ترميز stdout
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
        print("✅ تم ضبط ترميز stdout")
    
    return True

def test_arabic_fonts():
    """اختبار الخطوط العربية المتاحة"""
    
    print("\n🔤 اختبار الخطوط العربية")
    print("=" * 50)
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    font_db = QFontDatabase()
    all_families = font_db.families()
    
    # قائمة الخطوط العربية المعروفة
    arabic_fonts = [
        'Cairo',
        'Amiri',
        'Noto Sans Arabic',
        'Segoe UI',
        'Tahoma',
        'Arial Unicode MS',
        'Microsoft Sans Serif',
        'Times New Roman',
        'Arial',
        'Helvetica',
        'DejaVu Sans',
        'Liberation Sans'
    ]
    
    available_fonts = []
    for font_name in arabic_fonts:
        if font_name in all_families:
            available_fonts.append(font_name)
            print(f"✅ متاح: {font_name}")
        else:
            print(f"❌ غير متاح: {font_name}")
    
    if available_fonts:
        print(f"\n🎯 أفضل خط متاح: {available_fonts[0]}")
        return available_fonts[0]
    else:
        print("❌ لم يتم العثور على خطوط عربية مناسبة")
        return None

def create_arabic_test_window():
    """إنشاء نافذة اختبار للنص العربي"""
    
    print("\n🪟 إنشاء نافذة اختبار النص العربي")
    print("=" * 50)
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    # ضبط الاتجاه العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = QWidget()
    window.setWindowTitle("اختبار النص العربي - عيادة الأسنان")
    window.setGeometry(300, 300, 600, 400)
    window.setLayoutDirection(Qt.RightToLeft)
    
    # التخطيط
    layout = QVBoxLayout()
    layout.setAlignment(Qt.AlignRight)
    
    # النصوص التجريبية
    test_texts = [
        "عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان",
        "إدارة المرضى والمواعيد",
        "نظام الفواتير والمدفوعات",
        "تشخيص وعلاج أمراض الأسنان",
        "الأرقام: ١٢٣٤٥٦٧٨٩٠",
        "التاريخ: ٢٠٢٤/١٢/٢٥",
        "المبلغ: ٥٠٠ ريال سعودي"
    ]
    
    # اختبار خطوط مختلفة
    best_font = test_arabic_fonts()
    if best_font:
        font = QFont(best_font, 14)
        font.setStyleHint(QFont.System)
        
        for i, text in enumerate(test_texts):
            label = QLabel(text)
            label.setFont(font)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setStyleSheet("""
                QLabel {
                    padding: 10px;
                    border: 1px solid #ddd;
                    margin: 5px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
            """)
            layout.addWidget(label)
    
    # زر الإغلاق
    close_btn = QPushButton("إغلاق النافذة")
    close_btn.clicked.connect(window.close)
    close_btn.setStyleSheet("""
        QPushButton {
            padding: 10px 20px;
            font-size: 14px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
        }
        QPushButton:hover {
            background-color: #0056b3;
        }
    """)
    layout.addWidget(close_btn)
    
    window.setLayout(layout)
    window.show()
    
    print("✅ تم إنشاء نافذة الاختبار")
    print("👀 تحقق من عرض النص العربي في النافذة")
    
    return window, app

def main():
    """الدالة الرئيسية لإصلاح مشاكل اللغة العربية"""
    
    print("🔧 أداة إصلاح مشاكل اللغة العربية")
    print("=" * 60)
    
    # 1. تشخيص المشاكل
    diagnose_arabic_support()
    
    # 2. إصلاح الترميز
    fix_arabic_encoding()
    
    # 3. اختبار الخطوط
    best_font = test_arabic_fonts()
    
    # 4. إنشاء نافذة اختبار
    window, app = create_arabic_test_window()
    
    print("\n🎉 انتهى التشخيص والإصلاح")
    print("=" * 60)
    print("📋 ملخص النتائج:")
    print(f"   🔤 أفضل خط: {best_font or 'غير محدد'}")
    print(f"   🌍 Locale: {locale.getlocale()}")
    print(f"   📝 ترميز: UTF-8")
    print("\n💡 نصائح:")
    print("   - إذا كان النص غير واضح، جرب خط آخر")
    print("   - تأكد من تثبيت حزمة اللغة العربية في Windows")
    print("   - أعد تشغيل التطبيق بعد الإصلاحات")
    
    # تشغيل النافذة
    if app:
        app.exec_()

if __name__ == "__main__":
    # main()  # تم التعطيل مؤقتاً لمنع تنفيذ كود واجهة يسبب خطأ QApplication
