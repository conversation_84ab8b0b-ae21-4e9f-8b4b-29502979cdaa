# نظام إدارة عيادة الأسنان

## عيادة الدكتورة ضياء أبو جلبان لطب وجراحة الفم والأسنان

### 🦷 وصف المشروع

نظام شامل لإدارة عيادة الأسنان مطور بلغة Python باستخدام PyQt5، يوفر جميع الأدوات اللازمة لإدارة المرضى والمواعيد والعلاجات والفواتير.

### ✨ المميزات الرئيسية

- **🔐 نظام تسجيل دخول آمن** - حماية البيانات الطبية
- **👥 إدارة شاملة للمرضى** - إضافة، تعديل، بحث، وأرشفة
- **🦷 خريطة الأسنان التفاعلية** - نظام FDI للأسنان الدائمة واللبنية
- **📅 نظام المواعيد المتقدم** - جدولة يومية وأسبوعية
- **💊 إدارة العلاجات** - تسجيل العلاجات والتشخيصات
- **💰 نظام الفواتير** - دعم الدفعات الجزئية والمتابعة
- **📊 التقارير والإحصائيات** - تقارير مالية وطبية
- **🖼️ رفع الصور الطبية** - حفظ صور الأشعة والتشخيصات
- **🎨 تصميم عصري محسن** - واجهة مستخدم أنيقة مع تدرجات لونية وتأثيرات بصرية
- **🌐 دعم اللغة العربية المتقدم** - اتجاه من اليمين لليسار مع ترميز UTF-8
- **🔤 نظام الخطوط الموثوق** - استخدام خط Segoe UI الموثوق مع دعم Cairo
- **⚡ أداء محسن** - تحميل ذكي للخطوط وضبط الاتجاه العربي

### 🛠️ التقنيات المستخدمة

- **Python 3.6+** - لغة البرمجة الأساسية
- **PyQt5** - واجهة المستخدم الرسومية
- **SQLite** - قاعدة البيانات المحلية
- **Pillow** - معالجة الصور
- **ReportLab** - إنشاء التقارير PDF

### 📋 متطلبات التشغيل

```bash
Python 3.6 أو أحدث
PyQt5==5.15.9
Pillow==10.0.0
python-dateutil==2.8.2
reportlab==4.0.4
matplotlib==3.7.2
numpy==1.24.3
```

### 🚀 طريقة التثبيت والتشغيل

#### 1. تحميل المشروع
```bash
git clone [repository-url]
cd clinic9
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. تشغيل التطبيق
```bash
python main.py

### 🔧 حل مشاكل اللغة العربية

إذا كان النص العربي لا يظهر بشكل صحيح:

```bash
# تشخيص وإصلاح مشاكل النص العربي
python arabic_fix.py

# إعادة ضبط إعدادات اللغة العربية
python arabic_settings.py

# اختبار الخطوط المتاحة
python font_test.py
```

**للمزيد من المساعدة:** راجع ملف `ARABIC_TROUBLESHOOTING.md`
```

### 🔑 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** 123456

> ⚠️ **تنبيه أمني:** يُنصح بتغيير كلمة المرور الافتراضية فور التشغيل الأول

### 📁 هيكل المشروع

```
clinic9/
├── main.py                 # الملف الرئيسي
├── login_window.py         # نافذة تسجيل الدخول
├── main_window.py          # النافذة الرئيسية
├── patient_dialog.py       # نافذة إدارة المرضى
├── tooth_chart.py          # خريطة الأسنان التفاعلية
├── database.py             # إدارة قاعدة البيانات
├── styles.py               # أنماط التصميم
├── requirements.txt        # متطلبات المشروع
├── README.md              # دليل المستخدم
├── clinic_database.db     # قاعدة البيانات (تُنشأ تلقائياً)
└── patient_images/        # مجلد صور المرضى (يُنشأ تلقائياً)
```

### 🦷 نظام ترقيم الأسنان FDI

#### الأسنان الدائمة:
- **الربع الأول:** 11-18 (الفك العلوي الأيمن)
- **الربع الثاني:** 21-28 (الفك العلوي الأيسر)
- **الربع الثالث:** 31-38 (الفك السفلي الأيسر)
- **الربع الرابع:** 41-48 (الفك السفلي الأيمن)

#### الأسنان اللبنية:
- **الربع الأول:** 51-55 (الفك العلوي الأيمن)
- **الربع الثاني:** 61-65 (الفك العلوي الأيسر)
- **الربع الثالث:** 71-75 (الفك السفلي الأيسر)
- **الربع الرابع:** 81-85 (الفك السفلي الأيمن)

### 🎨 مفتاح ألوان حالات الأسنان

- 🟢 **أخضر:** سليم
- 🔴 **أحمر:** تسوس
- 🔵 **أزرق:** حشوة
- 🟡 **ذهبي:** تاج
- ⚫ **أسود:** خلع
- 🟣 **بنفسجي:** علاج عصب
- ⚪ **رمادي:** مفقود

### 📖 دليل الاستخدام

#### إضافة مريض جديد:
1. انقر على "مريض جديد" من الشريط الجانبي
2. أدخل البيانات الأساسية (الاسم، العمر، الجنس، الهاتف)
3. أضف الملاحظات الطبية والحساسية
4. ارفع صورة المريض (اختياري)
5. استخدم خريطة الأسنان لتسجيل الحالة الحالية
6. انقر "حفظ"

#### استخدام خريطة الأسنان:
1. اختر نوع الخريطة (دائمة أو لبنية)
2. انقر على أي سن لتعديل حالته
3. اختر الحالة من القائمة المنسدلة
4. أضف ملاحظات إضافية
5. انقر "حفظ"

### 🔧 الإعدادات والتخصيص

يمكن تخصيص الإعدادات التالية من خلال قاعدة البيانات:
- اسم العيادة
- اسم الطبيب
- ساعات العمل
- مدة الموعد الافتراضية

### 🛡️ الأمان والخصوصية

- جميع البيانات محفوظة محلياً
- تشفير كلمات المرور
- نسخ احتياطية منتظمة
- حماية البيانات الطبية

### 🎨 التحسينات الأخيرة (الإصدار الحالي)

#### نظام اللغة العربية المحسن والموثوق
- **حل مشاكل النص العربي**: إصلاح شامل لمشاكل عرض النص العربي
- **خط Segoe UI الموثوق**: استخدام خط Segoe UI كخط أساسي موثوق لـ Windows
- **ترميز UTF-8 محسن**: ضبط متقدم للترميز وmتغيرات البيئة
- **ضبط الاتجاه العربي**: اتجاه صحيح من اليمين لليسار في جميع النوافذ
- **أدوات التشخيص**: أدوات متقدمة لتشخيص وإصلاح مشاكل النص العربي
- **تحسين جودة النص**: خطوط محسنة مع دعم High DPI وتحسين العرض

#### تحسينات التصميم
- **تدرجات لونية عصرية**: خلفيات متدرجة للأزرار والبطاقات
- **أنماط محسنة للجداول**: ألوان متناوبة وتأثيرات التمرير
- **شريط جانبي محسن**: تصميم أنيق مع تأثيرات الاختيار
- **تبويبات متطورة**: تصميم عصري مع تدرجات لونية

#### تحسينات الأداء
- **تحميل ذكي للخطوط**: تجنب الأخطاء عند بدء التطبيق
- **إزالة التحذيرات**: تنظيف CSS لإزالة الخصائص غير المدعومة
- **استجابة أفضل**: تحسين سرعة الاستجابة للواجهة

### 🐛 الإبلاغ عن المشاكل

في حالة مواجهة أي مشاكل أو أخطاء، يرجى:
1. التأكد من تثبيت جميع المتطلبات
2. التحقق من إصدار Python
3. مراجعة ملف السجلات
4. التواصل مع فريق الدعم الفني

### 📞 الدعم الفني

للحصول على الدعم الفني أو طلب ميزات جديدة:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

### 📄 الترخيص

هذا المشروع مطور خصيصاً لعيادة الدكتورة ضياء أبو جلبان.
جميع الحقوق محفوظة © 2024

---

**تم التطوير بـ ❤️ لخدمة المجتمع الطبي**
