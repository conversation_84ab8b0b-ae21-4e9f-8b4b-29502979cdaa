"""
نافذة إضافة وتعديل المرضى
"""

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from styles import COLORS, BUTTON_STYLES, INPUT_STYLES
from tooth_chart import ToothChart
from database import DatabaseManager
import json
import os

class PatientDialog(QDialog):
    """نافذة إضافة/تعديل مريض"""
    
    def __init__(self, patient_id=None, parent=None):
        super().__init__(parent)
        self.patient_id = patient_id
        self.db = DatabaseManager()
        self.image_path = None
        # تفعيل أزرار التكبير والتصغير
        self.setWindowFlags(self.windowFlags() | Qt.WindowMinMaxButtonsHint)
        self.init_ui()
        if patient_id:
            self.load_patient_data()
            
    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        self.setWindowTitle("مريض جديد" if not self.patient_id else "تعديل بيانات المريض")
        self.showMaximized()
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(20)
        
        # الجانب الأيسر - بيانات المريض
        self.create_patient_info_section(main_layout)

        # الجانب الأيمن - خريطة الأسنان
        self.create_tooth_chart_section(main_layout)

        # تطبيق الأنماط
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {COLORS['background']};
            }}
        """)
        
    def create_patient_info_section(self, parent_layout):
        """إنشاء قسم بيانات المريض"""
        info_frame = QFrame()
        info_frame.setFixedWidth(450)
        info_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(info_frame)
        layout.setSpacing(15)
        
        # عنوان القسم
        title = QLabel("بيانات المريض")
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(title)
        
    # تم حذف قسم صورة المريض بناءً على طلب المستخدم
        
        # البيانات الأساسية
        self.create_basic_info_section(layout)
        
        # الملاحظات الطبية
        self.create_medical_notes_section(layout)
        
        # أزرار التحكم
        self.create_control_buttons(layout)
        
        parent_layout.addWidget(info_frame)
        
    def create_image_section(self, layout):
        """إنشاء قسم صورة المريض"""
        image_layout = QVBoxLayout()
        
        # عنوان
        image_label = QLabel("صورة المريض:")
        image_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['text_primary']};
                font-size: 14px;
                font-weight: 600;
            }}
        """)
        image_layout.addWidget(image_label)
        
        # منطقة عرض الصورة
        self.image_display = QLabel()
        self.image_display.setFixedSize(150, 150)
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setStyleSheet(f"""
            QLabel {{
                border: 2px dashed {COLORS['border']};
                border-radius: 8px;
                background-color: {COLORS['background']};
                color: {COLORS['text_secondary']};
            }}
        """)
        self.image_display.setText("لا توجد صورة")
        
        # أزرار الصورة
        image_buttons = QHBoxLayout()
        
        upload_btn = QPushButton("📁 رفع صورة")
        upload_btn.setStyleSheet(BUTTON_STYLES['outline'])
        upload_btn.clicked.connect(self.upload_image)
        image_buttons.addWidget(upload_btn)
        
        remove_btn = QPushButton("🗑️ حذف")
        remove_btn.setStyleSheet(BUTTON_STYLES['danger'])
        remove_btn.clicked.connect(self.remove_image)
        image_buttons.addWidget(remove_btn)
        
        image_layout.addWidget(self.image_display)
        image_layout.addLayout(image_buttons)
        layout.addLayout(image_layout)
        
    def create_basic_info_section(self, layout):
        """إنشاء قسم البيانات الأساسية"""
        
        # الاسم
        name_label = QLabel("الاسم الكامل:")
        name_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        layout.addWidget(name_label)
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل الاسم الكامل")
        self.name_input.setStyleSheet(INPUT_STYLES)
        layout.addWidget(self.name_input)
        
        # العمر والجنس في صف واحد
        age_gender_layout = QHBoxLayout()
        
        # العمر
        age_layout = QVBoxLayout()
        age_label = QLabel("العمر:")
        age_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        age_layout.addWidget(age_label)
        
        self.age_input = QSpinBox()
        self.age_input.setRange(1, 120)
        self.age_input.setValue(25)
        self.age_input.setStyleSheet(INPUT_STYLES)
        age_layout.addWidget(self.age_input)
        
        age_gender_layout.addLayout(age_layout)
        
        # الجنس
        gender_layout = QVBoxLayout()
        gender_label = QLabel("الجنس:")
        gender_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        gender_layout.addWidget(gender_label)
        
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["ذكر", "أنثى"])
        self.gender_combo.setStyleSheet(INPUT_STYLES)
        gender_layout.addWidget(self.gender_combo)
        
        age_gender_layout.addLayout(gender_layout)
        layout.addLayout(age_gender_layout)
        
        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        layout.addWidget(phone_label)
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("مثال: **********")
        self.phone_input.setStyleSheet(INPUT_STYLES)
        layout.addWidget(self.phone_input)
        
    def create_medical_notes_section(self, layout):
        """إنشاء قسم الملاحظات الطبية"""
        
        notes_label = QLabel("الملاحظات الطبية والحساسية:")
        notes_label.setStyleSheet(f"color: {COLORS['text_primary']}; font-weight: 600;")
        layout.addWidget(notes_label)
        
        self.medical_notes = QTextEdit()
        self.medical_notes.setPlaceholderText("أدخل الملاحظات الطبية، الحساسية، الأمراض المزمنة...")
        self.medical_notes.setMaximumHeight(100)
        self.medical_notes.setStyleSheet(INPUT_STYLES)
        layout.addWidget(self.medical_notes)
        
    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        
        layout.addStretch()
        
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(BUTTON_STYLES['primary'])
        save_btn.clicked.connect(self.save_patient)
        buttons_layout.addWidget(save_btn)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet(BUTTON_STYLES['outline'])
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def create_tooth_chart_section(self, parent_layout):
        """إنشاء قسم خريطة الأسنان"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['surface']};
                border: 1px solid {COLORS['border']};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(chart_frame)
        
        # عنوان القسم
        title = QLabel("خريطة الأسنان")
        title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }}
        """)
        layout.addWidget(title)
        
        # اختيار نوع الخريطة
        chart_type_layout = QHBoxLayout()
        
        self.permanent_radio = QRadioButton("الأسنان الدائمة")
        self.permanent_radio.setChecked(True)
        self.permanent_radio.toggled.connect(self.change_chart_type)
        chart_type_layout.addWidget(self.permanent_radio)
        
        self.deciduous_radio = QRadioButton("الأسنان اللبنية")
        self.deciduous_radio.toggled.connect(self.change_chart_type)
        chart_type_layout.addWidget(self.deciduous_radio)
        
        chart_type_layout.addStretch()
        layout.addLayout(chart_type_layout)
        
        # خريطة الأسنان
        self.tooth_chart = ToothChart("permanent")
        layout.addWidget(self.tooth_chart)
        
    # زر حفظ بيانات المريض أسفل خريطة الأسنان
    save_btn = QPushButton("💾 حفظ بيانات المريض")
    save_btn.setStyleSheet(f"background-color: #2ecc40; color: white; font-size: 18px; font-weight: bold; border-radius: 8px; padding: 10px 0;")
    save_btn.clicked.connect(self.save_patient)
    layout.addWidget(save_btn)

    parent_layout.addWidget(chart_frame)
        
    def change_chart_type(self):
        """تغيير نوع خريطة الأسنان"""
        if self.permanent_radio.isChecked():
            chart_type = "permanent"
        else:
            chart_type = "deciduous"
            
        # حفظ البيانات الحالية (اختياري)
        # current_data = self.tooth_chart.get_teeth_data()

        # إزالة الخريطة القديمة من التخطيط
        old_chart = self.tooth_chart
        parent = old_chart.parent()
        if parent:
            layout = parent.layout()
            layout.removeWidget(old_chart)
            old_chart.deleteLater()

        # إنشاء وإضافة الخريطة الجديدة
        self.tooth_chart = ToothChart(chart_type)
        if parent:
            layout.addWidget(self.tooth_chart)
        
    def upload_image(self):
        """رفع صورة المريض"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "اختر صورة المريض",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        
        if file_path:
            # إنشاء مجلد الصور إذا لم يكن موجوداً
            images_dir = "patient_images"
            if not os.path.exists(images_dir):
                os.makedirs(images_dir)
            
            # نسخ الصورة إلى مجلد التطبيق
            import shutil
            filename = os.path.basename(file_path)
            new_path = os.path.join(images_dir, filename)
            shutil.copy2(file_path, new_path)
            
            self.image_path = new_path
            
            # عرض الصورة
            pixmap = QPixmap(new_path)
            scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.image_display.setPixmap(scaled_pixmap)
            
    def remove_image(self):
        """حذف صورة المريض"""
        self.image_path = None
        self.image_display.clear()
        self.image_display.setText("لا توجد صورة")
        
    def save_patient(self):
        """حفظ بيانات المريض"""
        # التحقق من البيانات
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المريض")
            return
            
        if not self.phone_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            return
        
        # جمع البيانات
        patient_data = {
            'name': self.name_input.text().strip(),
            'age': self.age_input.value(),
            'gender': self.gender_combo.currentText(),
            'phone': self.phone_input.text().strip(),
            'medical_notes': self.medical_notes.toPlainText(),
            'image_path': self.image_path
        }
        
        try:
            if self.patient_id:
                # تحديث مريض موجود
                self.db.update_data('patients', patient_data, f'id = {self.patient_id}')
                patient_id = self.patient_id
            else:
                # إضافة مريض جديد
                patient_id = self.db.insert_data('patients', patient_data)
            
            # حفظ بيانات خريطة الأسنان
            self.save_tooth_chart_data(patient_id)
            
            QMessageBox.information(self, "نجح", "تم حفظ بيانات المريض بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
            
    def save_tooth_chart_data(self, patient_id):
        """حفظ بيانات خريطة الأسنان"""
        teeth_data = self.tooth_chart.get_teeth_data()
        
        # حذف البيانات القديمة
        self.db.delete_data('tooth_chart', f'patient_id = {patient_id}')
        
        # إدراج البيانات الجديدة
        for tooth_number, data in teeth_data.items():
            if data['condition'] != 'healthy' or data['notes']:
                tooth_data = {
                    'patient_id': patient_id,
                    'tooth_number': tooth_number,
                    'tooth_type': data['type'],
                    'condition_status': data['condition'],
                    'notes': data['notes'],
                    'color_code': data['condition']
                }
                self.db.insert_data('tooth_chart', tooth_data)
                
    def load_patient_data(self):
        """تحميل بيانات المريض للتعديل"""
        if not self.patient_id:
            return
            
        # تحميل البيانات الأساسية
        patient = self.db.execute_query(
            "SELECT * FROM patients WHERE id = ?", 
            (self.patient_id,)
        )
        
        if patient:
            patient = patient[0]
            self.name_input.setText(patient[1])
            self.age_input.setValue(patient[2])
            self.gender_combo.setCurrentText(patient[3])
            self.phone_input.setText(patient[4])
            self.medical_notes.setPlainText(patient[5] or "")
            
            # تحميل الصورة
            if patient[6]:
                self.image_path = patient[6]
                if os.path.exists(patient[6]):
                    pixmap = QPixmap(patient[6])
                    scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.image_display.setPixmap(scaled_pixmap)
        
        # تحميل بيانات خريطة الأسنان
        self.load_tooth_chart_data()
        
    def load_tooth_chart_data(self):
        """تحميل بيانات خريطة الأسنان"""
        teeth_data = self.db.execute_query(
            "SELECT tooth_number, condition_status, notes FROM tooth_chart WHERE patient_id = ?",
            (self.patient_id,)
        )
        
        chart_data = {}
        for tooth in teeth_data:
            chart_data[tooth[0]] = {
                'condition': tooth[1],
                'notes': tooth[2] or '',
                'type': 'permanent'  # يمكن تحسينه لاحقاً
            }
            
        self.tooth_chart.load_teeth_data(chart_data)
